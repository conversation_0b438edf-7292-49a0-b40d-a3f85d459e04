# Enhanced SARIMAX Model with External Regressors

## Overview

This enhanced implementation of the ARIMA monthly predictor now supports external regressors (exogenous variables) in SARIMAX models based on correlation analysis results. The system automatically identifies statistically significant weather features correlated with electricity consumption and incorporates them into the prediction model.

## Key Features

### 1. Correlation-Based Feature Selection
- **Automatic Loading**: Automatically detects and loads the most recent correlation analysis results
- **Configurable Thresholds**: Supports customizable correlation coefficient and p-value thresholds
- **Statistical Validation**: Only includes features with significant correlations (p < 0.05 by default)

### 2. External Regressors Integration
- **SARIMAX Enhancement**: Seamlessly integrates external regressors into SARIMAX models
- **Data Alignment**: Automatically aligns weather data with electricity consumption data
- **Forecast Generation**: Generates external regressor values for prediction periods using historical seasonal patterns

### 3. Fallback Mechanisms
- **Graceful Degradation**: Falls back to standard SARIMAX when correlation data is unavailable
- **Threshold-Based Filtering**: Automatically switches to standard model when no features meet criteria
- **Backward Compatibility**: Maintains full compatibility with existing ARIMA/SARIMAX functionality

## Configuration Options

### Constructor Parameters
```python
ARIMAMonthlyPredictor(
    data_path=None,
    target_column='代理购电',
    model_type='SARIMAX',
    use_external_regressors=True,      # Enable/disable external regressors
    correlation_threshold=0.3,          # Minimum correlation coefficient (absolute value)
    p_value_threshold=0.05             # Maximum p-value for significance
)
```

### Feature Selection Criteria
- **Correlation Threshold**: Minimum absolute correlation coefficient (default: 0.3)
- **P-value Threshold**: Maximum p-value for statistical significance (default: 0.05)
- **Significance Flag**: Must be marked as statistically significant in correlation analysis

## Usage Examples

### 1. Basic Usage with External Regressors
```python
# Create predictor with external regressors enabled
predictor = ARIMAMonthlyPredictor(
    target_column='代理购电',
    model_type='SARIMAX',
    use_external_regressors=True,
    correlation_threshold=0.3
)

# Load data and fit model
predictor.load_and_prepare_data()
predictor.fit_model()

# Make predictions
predictions = predictor.predict_future(periods=6)
```

### 2. Custom Threshold Configuration
```python
# Use stricter criteria for feature selection
predictor = ARIMAMonthlyPredictor(
    target_column='农业',
    model_type='SARIMAX',
    use_external_regressors=True,
    correlation_threshold=0.4,    # Higher correlation requirement
    p_value_threshold=0.01       # Stricter significance requirement
)
```

### 3. Backward Compatibility
```python
# Standard ARIMA model (unchanged)
predictor_arima = ARIMAMonthlyPredictor(
    target_column='代理购电',
    model_type='ARIMA',
    use_external_regressors=False
)

# Standard SARIMAX without external regressors
predictor_sarimax = ARIMAMonthlyPredictor(
    target_column='代理购电',
    model_type='SARIMAX',
    use_external_regressors=False
)
```

## Implementation Details

### 1. CorrelationFeatureSelector Class
- **Purpose**: Handles loading and processing of correlation analysis results
- **Key Methods**:
  - `load_correlation_results()`: Loads correlation analysis CSV files
  - `load_monthly_data()`: Loads monthly aggregated weather-electricity data
  - `get_selected_features()`: Applies selection criteria and returns feature list
  - `get_feature_data()`: Extracts aligned feature data for modeling

### 2. Enhanced Data Loading
- **Automatic Detection**: Finds most recent correlation and monthly data files
- **Data Alignment**: Ensures temporal consistency between target and feature data
- **Missing Value Handling**: Gracefully handles missing or unavailable features

### 3. Model Integration
- **Parameter Search**: Enhanced parameter search includes external regressors
- **Prediction Logic**: Generates future external regressor values using seasonal patterns
- **Evaluation Support**: Cross-validation includes external regressors in train/test splits

## File Structure

```
ARIMA/
├── arima_monthly_predictor.py          # Enhanced main predictor class
├── test_enhanced_sarimax.py            # Comprehensive test suite
├── README_Enhanced_SARIMAX.md          # This documentation
└── results/                            # Model outputs and predictions
    ├── arima_predictions_*_with_exog_*.csv  # Predictions with external regressors
    └── model_diagnostics_*.png              # Diagnostic plots
```

## Expected Input Files

### 1. Correlation Analysis Results
- **File Pattern**: `feature_analysis_results/monthly_significant_pearson_correlations_*.csv`
- **Required Columns**:
  - `electricity_type`: Target electricity category
  - `weather_variable`: Weather feature name
  - `pearson_correlation`: Correlation coefficient
  - `pearson_abs_corr`: Absolute correlation coefficient
  - `pearson_p_value`: Statistical significance p-value
  - `pearson_significant`: Boolean significance flag

### 2. Monthly Aggregated Data
- **File Pattern**: `feature_analysis_results/monthly_aggregated_data_*.csv`
- **Required Columns**:
  - `年月_str`: Date in YYYY-MM format
  - Electricity columns: `居民`, `农业`, `代理购电`
  - Weather feature columns (as identified in correlation analysis)

## Output Enhancements

### 1. Prediction Files
- **Naming Convention**: Includes `_with_exog` suffix when external regressors are used
- **Additional Logging**: Reports which external regressors are being used
- **Forecast Method**: Indicates whether external regressors were used in prediction

### 2. Model Information
- **Configuration Display**: Shows current settings and thresholds
- **Feature Reporting**: Lists selected external regressors and their correlations
- **Fallback Notifications**: Clearly indicates when fallback to standard models occurs

## Testing and Validation

The implementation includes comprehensive testing:

### 1. Feature Selection Testing
- Tests different correlation thresholds and p-value criteria
- Validates feature selection for different electricity types
- Confirms proper handling of missing correlation data

### 2. Model Integration Testing
- Verifies SARIMAX parameter search with external regressors
- Tests prediction generation with external regressor forecasting
- Validates model evaluation with external regressors

### 3. Backward Compatibility Testing
- Confirms standard ARIMA models work unchanged
- Tests standard SARIMAX models without external regressors
- Validates fallback mechanisms when correlation data is unavailable

## Performance Considerations

### 1. Parameter Search
- External regressors increase parameter search time
- Grid search explores same parameter space but with additional exogenous variables
- Consider reducing search ranges for faster execution

### 2. Memory Usage
- Additional memory required for external regressor data
- Feature data is loaded and aligned with target data
- Memory usage scales with number of selected features

### 3. Prediction Speed
- Forecast generation requires external regressor value prediction
- Seasonal pattern analysis adds computational overhead
- Overall prediction time increases modestly with external regressors

## Troubleshooting

### Common Issues and Solutions

1. **No Features Selected**
   - Check correlation threshold settings
   - Verify correlation analysis results are available
   - Consider lowering thresholds or checking data quality

2. **Data Alignment Issues**
   - Ensure monthly aggregated data covers same time period as target data
   - Check date format consistency in input files
   - Verify feature names match between correlation results and monthly data

3. **Model Convergence Issues**
   - Some parameter combinations may not converge with external regressors
   - System automatically tries alternative parameters
   - Consider reducing the number of external regressors

4. **Prediction Errors**
   - External regressor forecasting may fail for some features
   - System falls back to using last known values
   - Check for sufficient historical data for seasonal pattern analysis

## Future Enhancements

Potential improvements for future versions:

1. **Advanced Forecasting**: More sophisticated external regressor forecasting methods
2. **Feature Engineering**: Automatic creation of derived weather features
3. **Model Selection**: Automatic comparison between models with/without external regressors
4. **Performance Optimization**: Caching and parallel processing for parameter search
5. **Visualization**: Enhanced plots showing external regressor contributions
