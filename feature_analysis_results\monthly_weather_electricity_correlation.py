#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Monthly-Level Weather-Electricity Correlation Analysis
======================================================

This script performs comprehensive correlation analysis between monthly weather data
and monthly electricity consumption data directly from monthly data sheets for three categories:
居民 (residential), 农业 (agricultural), and 代理购电 (proxy electricity purchase).

Features:
- Direct reading of monthly electricity data from Excel sheets
- Comprehensive monthly weather variables analysis
- Pearson and Spearman correlation coefficients
- Statistical significance testing (p-values)
- Correlation heatmaps
- Structured CSV output with correlation results
- Filtering for statistically significant high correlations
- Timestamped output files

Author: Augment Agent
Date: 2025-07-17
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import pearsonr, spearmanr
import os
from datetime import datetime
import warnings

warnings.filterwarnings('ignore')

# Set Chinese font for matplotlib
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class MonthlyWeatherElectricityCorrelation:
    """Monthly-level weather-electricity correlation analysis"""
    
    def __init__(self,
                 electricity_data_path='data/龙泉代理购电.xlsx',
                 monthly_weather_path='data/丽水市_月度天气数据_20250717_171422.xlsx'):
        """
        Initialize the correlation analyzer

        Args:
            electricity_data_path: Path to the electricity data file (contains monthly data sheet)
            monthly_weather_path: Path to the monthly weather data file
        """
        self.electricity_data_path = electricity_data_path
        self.monthly_weather_path = monthly_weather_path
        self.monthly_weather_df = None
        self.monthly_electricity_df = None
        self.merged_monthly_df = None
        self.weather_columns = []
        self.electricity_columns = ['居民', '农业', '代理购电']
        self.correlation_results = {}
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
    def load_and_prepare_data(self):
        """Load and prepare monthly data directly"""
        print(f"Loading monthly data...")
        print(f"  Electricity data: {self.electricity_data_path}")
        print(f"  Monthly weather data: {self.monthly_weather_path}")

        # Load monthly electricity data directly from the '月数据' sheet
        self.monthly_electricity_df = pd.read_excel(self.electricity_data_path, sheet_name='月数据')

        # Load monthly weather data
        self.monthly_weather_df = pd.read_excel(self.monthly_weather_path)

        # Prepare monthly electricity data
        self.prepare_monthly_electricity_data()

        # Merge monthly weather and electricity data
        self.merge_monthly_data()

        print(f"Data preparation completed:")
        print(f"  - Monthly weather records: {len(self.monthly_weather_df)}")
        print(f"  - Monthly electricity records: {len(self.monthly_electricity_df)}")
        print(f"  - Merged monthly records: {len(self.merged_monthly_df)}")

        return self.merged_monthly_df
    
    def prepare_monthly_electricity_data(self):
        """Prepare monthly electricity data for analysis"""
        print("Preparing monthly electricity data...")

        # Convert month format from YYYYMM to YYYY-MM for consistency
        self.monthly_electricity_df['年月'] = pd.to_datetime(
            self.monthly_electricity_df['月份'].astype(str), format='%Y%m'
        ).dt.strftime('%Y-%m')

        # Extract year and month for additional analysis if needed
        self.monthly_electricity_df['年'] = pd.to_datetime(
            self.monthly_electricity_df['月份'].astype(str), format='%Y%m'
        ).dt.year
        self.monthly_electricity_df['月'] = pd.to_datetime(
            self.monthly_electricity_df['月份'].astype(str), format='%Y%m'
        ).dt.month

        print(f"Monthly electricity data preparation completed:")
        print(f"  Date range: {self.monthly_electricity_df['年月'].min()} to {self.monthly_electricity_df['年月'].max()}")
        print(f"  Records: {len(self.monthly_electricity_df)}")
        print(f"  Columns: {list(self.monthly_electricity_df.columns)}")

        return self.monthly_electricity_df
    
    def merge_monthly_data(self):
        """Merge monthly weather and electricity data"""
        print("Merging monthly weather and electricity data...")

        # Prepare monthly weather data for merging
        weather_df = self.monthly_weather_df.copy()

        # The weather data already has '年月' column in YYYY-MM format
        # Ensure it's in the same format as electricity data
        if '年月' in weather_df.columns:
            # Weather data already has the correct format
            weather_df['年月_str'] = weather_df['年月'].astype(str)
        else:
            # Fallback: create from separate year and month columns if they exist
            if '年份' in weather_df.columns and '月份' in weather_df.columns:
                weather_df['年月_str'] = weather_df['年份'].astype(str) + '-' + weather_df['月份'].astype(str).str.zfill(2)
            else:
                raise ValueError("Weather data must have either '年月' column or '年份'+'月份' columns")

        # Identify weather columns (exclude date-related and metadata columns)
        exclude_cols = ['年月', '年份', '月份', '年月_str', '月天数']
        self.weather_columns = [col for col in weather_df.columns if col not in exclude_cols]

        print(f"Weather columns to analyze: {self.weather_columns}")

        # Merge on year-month
        self.merged_monthly_df = pd.merge(
            self.monthly_electricity_df,
            weather_df,
            left_on='年月',
            right_on='年月_str',
            how='inner'
        )

        print(f"Monthly data merge completed:")
        print(f"  Weather variables: {len(self.weather_columns)}")
        print(f"  Electricity categories: {len(self.electricity_columns)}")
        print(f"  Merged records: {len(self.merged_monthly_df)}")
        print(f"  Merged columns: {list(self.merged_monthly_df.columns)}")

        # Check which date column is available after merge
        date_col = None
        for col in ['年月', '年月_str', '年月_x', '年月_y']:
            if col in self.merged_monthly_df.columns:
                date_col = col
                break

        if date_col:
            print(f"  Date range: {self.merged_monthly_df[date_col].min()} to {self.merged_monthly_df[date_col].max()}")
        else:
            print("  Warning: No date column found after merge")

        return self.merged_monthly_df
    
    def calculate_correlations(self):
        """Calculate Pearson and Spearman correlations with p-values"""
        print("\nCalculating monthly correlations...")
        
        results = []
        
        for elec_col in self.electricity_columns:
            for weather_col in self.weather_columns:
                # Get clean data (remove NaN values)
                elec_data = self.merged_monthly_df[elec_col].dropna()
                weather_data = self.merged_monthly_df[weather_col].dropna()
                
                # Find common indices
                common_idx = elec_data.index.intersection(weather_data.index)
                if len(common_idx) < 5:  # Need at least 5 data points for monthly data
                    continue
                
                elec_clean = elec_data.loc[common_idx]
                weather_clean = weather_data.loc[common_idx]
                
                # Calculate Pearson correlation
                try:
                    pearson_corr, pearson_p = pearsonr(weather_clean, elec_clean)
                except:
                    pearson_corr, pearson_p = np.nan, np.nan
                
                # Calculate Spearman correlation
                try:
                    spearman_corr, spearman_p = spearmanr(weather_clean, elec_clean)
                except:
                    spearman_corr, spearman_p = np.nan, np.nan
                
                # Store results
                result = {
                    'electricity_type': elec_col,
                    'weather_variable': weather_col,
                    'pearson_correlation': pearson_corr,
                    'pearson_p_value': pearson_p,
                    'spearman_correlation': spearman_corr,
                    'spearman_p_value': spearman_p,
                    'sample_size': len(common_idx),
                    'pearson_significant': pearson_p < 0.05 if not np.isnan(pearson_p) else False,
                    'spearman_significant': spearman_p < 0.05 if not np.isnan(spearman_p) else False,
                    'pearson_abs_corr': abs(pearson_corr) if not np.isnan(pearson_corr) else 0,
                    'spearman_abs_corr': abs(spearman_corr) if not np.isnan(spearman_corr) else 0
                }
                
                results.append(result)
        
        self.correlation_results = pd.DataFrame(results)
        print(f"Calculated correlations for {len(results)} weather-electricity pairs")
        
        return self.correlation_results
    
    def filter_significant_correlations(self, min_correlation=0.3, significance_level=0.05):
        """Filter for statistically significant high correlations"""
        print(f"\nFiltering for significant correlations (|r| >= {min_correlation}, p < {significance_level})...")
        
        # Filter for Pearson correlations
        pearson_significant = self.correlation_results[
            (self.correlation_results['pearson_abs_corr'] >= min_correlation) &
            (self.correlation_results['pearson_p_value'] < significance_level)
        ].copy()
        
        # Filter for Spearman correlations
        spearman_significant = self.correlation_results[
            (self.correlation_results['spearman_abs_corr'] >= min_correlation) &
            (self.correlation_results['spearman_p_value'] < significance_level)
        ].copy()
        
        print(f"Found {len(pearson_significant)} significant Pearson correlations")
        print(f"Found {len(spearman_significant)} significant Spearman correlations")
        
        return pearson_significant, spearman_significant

    def create_correlation_heatmaps(self):
        """Create correlation heatmaps for visualization"""
        print("\nCreating monthly correlation heatmaps...")

        # Prepare data for overall heatmap
        pearson_data = []
        spearman_data = []

        for _, row in self.correlation_results.iterrows():
            pearson_data.append({
                'Weather_Variable': row['weather_variable'],
                row['electricity_type']: row['pearson_correlation']
            })
            spearman_data.append({
                'Weather_Variable': row['weather_variable'],
                row['electricity_type']: row['spearman_correlation']
            })

        # Convert to DataFrames
        pearson_df = pd.DataFrame(pearson_data).groupby('Weather_Variable').first()
        spearman_df = pd.DataFrame(spearman_data).groupby('Weather_Variable').first()

        # Create combined heatmap
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 20))

        # Pearson heatmap
        sns.heatmap(pearson_df, annot=True, cmap='RdBu_r', center=0,
                   fmt='.3f', ax=ax1, cbar_kws={'label': 'Pearson Correlation'})
        ax1.set_title('Monthly Pearson Correlations: Weather Variables vs Electricity Types')
        ax1.set_xlabel('Electricity Types')
        ax1.set_ylabel('Weather Variables')

        # Spearman heatmap
        sns.heatmap(spearman_df, annot=True, cmap='RdBu_r', center=0,
                   fmt='.3f', ax=ax2, cbar_kws={'label': 'Spearman Correlation'})
        ax2.set_title('Monthly Spearman Correlations: Weather Variables vs Electricity Types')
        ax2.set_xlabel('Electricity Types')
        ax2.set_ylabel('Weather Variables')

        plt.tight_layout()

        # Save overall heatmap
        heatmap_filename = f'feature_analysis_results/monthly_correlation_heatmap_{self.timestamp}.png'
        plt.savefig(heatmap_filename, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"Saved monthly correlation heatmap: {heatmap_filename}")

        # Create separate heatmaps for significant correlations
        self.create_significant_correlation_heatmap()

        return heatmap_filename

    def create_significant_correlation_heatmap(self):
        """Create heatmap showing only significant correlations"""
        print("Creating significant correlations heatmap...")

        # Get significant correlations
        pearson_sig, spearman_sig = self.filter_significant_correlations()

        if len(pearson_sig) > 0 or len(spearman_sig) > 0:
            fig, axes = plt.subplots(1, 2, figsize=(20, 10))

            # Significant Pearson correlations
            if len(pearson_sig) > 0:
                pearson_sig_pivot = pearson_sig.pivot_table(
                    index='weather_variable',
                    columns='electricity_type',
                    values='pearson_correlation'
                )

                sns.heatmap(pearson_sig_pivot, annot=True, cmap='RdBu_r', center=0,
                           fmt='.3f', ax=axes[0], cbar_kws={'label': 'Pearson Correlation'})
                axes[0].set_title('Significant Monthly Pearson Correlations (p<0.05, |r|≥0.3)')
                axes[0].set_xlabel('Electricity Types')
                axes[0].set_ylabel('Weather Variables')
            else:
                axes[0].text(0.5, 0.5, 'No significant Pearson correlations found',
                           ha='center', va='center', transform=axes[0].transAxes)
                axes[0].set_title('Significant Monthly Pearson Correlations')

            # Significant Spearman correlations
            if len(spearman_sig) > 0:
                spearman_sig_pivot = spearman_sig.pivot_table(
                    index='weather_variable',
                    columns='electricity_type',
                    values='spearman_correlation'
                )

                sns.heatmap(spearman_sig_pivot, annot=True, cmap='RdBu_r', center=0,
                           fmt='.3f', ax=axes[1], cbar_kws={'label': 'Spearman Correlation'})
                axes[1].set_title('Significant Monthly Spearman Correlations (p<0.05, |r|≥0.3)')
                axes[1].set_xlabel('Electricity Types')
                axes[1].set_ylabel('Weather Variables')
            else:
                axes[1].text(0.5, 0.5, 'No significant Spearman correlations found',
                           ha='center', va='center', transform=axes[1].transAxes)
                axes[1].set_title('Significant Monthly Spearman Correlations')

            plt.tight_layout()

            # Save significant correlations heatmap
            sig_heatmap_filename = f'feature_analysis_results/monthly_significant_correlations_heatmap_{self.timestamp}.png'
            plt.savefig(sig_heatmap_filename, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"Saved significant correlations heatmap: {sig_heatmap_filename}")

            return sig_heatmap_filename
        else:
            print("No significant correlations found for heatmap creation")
            return None

    def save_results(self):
        """Save correlation results to CSV files"""
        print("\nSaving monthly correlation results...")

        # Save complete results
        complete_filename = f'feature_analysis_results/monthly_weather_electricity_correlations_{self.timestamp}.csv'
        self.correlation_results.to_csv(complete_filename, index=False, encoding='utf-8-sig')
        print(f"Saved complete results: {complete_filename}")

        # Save significant correlations
        pearson_sig, spearman_sig = self.filter_significant_correlations()

        pearson_filename = None
        spearman_filename = None

        if len(pearson_sig) > 0:
            pearson_filename = f'feature_analysis_results/monthly_significant_pearson_correlations_{self.timestamp}.csv'
            pearson_sig.to_csv(pearson_filename, index=False, encoding='utf-8-sig')
            print(f"Saved significant Pearson correlations: {pearson_filename}")

        if len(spearman_sig) > 0:
            spearman_filename = f'feature_analysis_results/monthly_significant_spearman_correlations_{self.timestamp}.csv'
            spearman_sig.to_csv(spearman_filename, index=False, encoding='utf-8-sig')
            print(f"Saved significant Spearman correlations: {spearman_filename}")

        # Save monthly aggregated data for reference
        monthly_data_filename = f'feature_analysis_results/monthly_aggregated_data_{self.timestamp}.csv'
        self.merged_monthly_df.to_csv(monthly_data_filename, index=False, encoding='utf-8-sig')
        print(f"Saved monthly aggregated data: {monthly_data_filename}")

        return complete_filename, pearson_filename, spearman_filename, monthly_data_filename

    def generate_summary_report(self):
        """Generate a summary report of the monthly correlation analysis"""
        print("\nGenerating monthly summary report...")

        # Calculate summary statistics
        summary_stats = {}

        for elec_type in self.electricity_columns:
            elec_data = self.correlation_results[
                self.correlation_results['electricity_type'] == elec_type
            ]

            if len(elec_data) == 0:
                continue

            # Pearson statistics
            pearson_corrs = elec_data['pearson_correlation'].dropna()
            pearson_sig_count = elec_data['pearson_significant'].sum()

            # Spearman statistics
            spearman_corrs = elec_data['spearman_correlation'].dropna()
            spearman_sig_count = elec_data['spearman_significant'].sum()

            summary_stats[elec_type] = {
                'total_weather_variables': len(elec_data),
                'pearson_mean_abs_corr': pearson_corrs.abs().mean(),
                'pearson_max_abs_corr': pearson_corrs.abs().max(),
                'pearson_significant_count': pearson_sig_count,
                'spearman_mean_abs_corr': spearman_corrs.abs().mean(),
                'spearman_max_abs_corr': spearman_corrs.abs().max(),
                'spearman_significant_count': spearman_sig_count
            }

        # Save summary report
        summary_df = pd.DataFrame(summary_stats).T
        summary_filename = f'feature_analysis_results/monthly_correlation_summary_{self.timestamp}.csv'
        summary_df.to_csv(summary_filename, encoding='utf-8-sig')

        print(f"Saved summary report: {summary_filename}")

        # Print summary to console
        print("\n" + "="*80)
        print("MONTHLY WEATHER-ELECTRICITY CORRELATION ANALYSIS SUMMARY")
        print("="*80)

        for elec_type in self.electricity_columns:
            if elec_type in summary_stats:
                stats = summary_stats[elec_type]
                print(f"\n{elec_type} (Electricity Type):")
                print(f"  Total weather variables analyzed: {stats['total_weather_variables']}")
                print(f"  Pearson correlations:")
                print(f"    Mean |correlation|: {stats['pearson_mean_abs_corr']:.3f}")
                print(f"    Max |correlation|: {stats['pearson_max_abs_corr']:.3f}")
                print(f"    Significant correlations (p<0.05): {stats['pearson_significant_count']}")
                print(f"  Spearman correlations:")
                print(f"    Mean |correlation|: {stats['spearman_mean_abs_corr']:.3f}")
                print(f"    Max |correlation|: {stats['spearman_max_abs_corr']:.3f}")
                print(f"    Significant correlations (p<0.05): {stats['spearman_significant_count']}")

        return summary_filename

    def run_complete_analysis(self):
        """Run the complete monthly correlation analysis"""
        print("="*80)
        print("MONTHLY WEATHER-ELECTRICITY CORRELATION ANALYSIS")
        print("="*80)

        # Load and prepare data
        self.load_and_prepare_data()

        # Calculate correlations
        self.calculate_correlations()

        # Create visualizations
        self.create_correlation_heatmaps()

        # Save results
        complete_file, pearson_file, spearman_file, monthly_data_file = self.save_results()

        # Generate summary
        summary_file = self.generate_summary_report()

        print("\n" + "="*80)
        print("MONTHLY ANALYSIS COMPLETE!")
        print("="*80)
        print("Generated files:")
        print(f"  - Complete results: {complete_file}")
        if pearson_file:
            print(f"  - Significant Pearson correlations: {pearson_file}")
        if spearman_file:
            print(f"  - Significant Spearman correlations: {spearman_file}")
        print(f"  - Summary report: {summary_file}")
        print(f"  - Monthly aggregated data: {monthly_data_file}")
        print(f"  - Correlation heatmaps: feature_analysis_results/monthly_*_heatmap_{self.timestamp}.png")

        return {
            'complete_results': complete_file,
            'significant_pearson': pearson_file,
            'significant_spearman': spearman_file,
            'summary': summary_file,
            'monthly_data': monthly_data_file,
            'timestamp': self.timestamp
        }


def main():
    """Main execution function"""
    try:
        # Initialize analyzer
        analyzer = MonthlyWeatherElectricityCorrelation()

        # Run complete analysis
        results = analyzer.run_complete_analysis()

        print(f"\nMonthly correlation analysis completed successfully!")
        print(f"Results saved with timestamp: {results['timestamp']}")

    except Exception as e:
        print(f"Error during analysis: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
