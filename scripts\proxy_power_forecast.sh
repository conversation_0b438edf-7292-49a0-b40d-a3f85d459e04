#!/bin/bash

export CUDA_VISIBLE_DEVICES=0

model_name=PatchTST
dataset=./data/
gpu=0
seq_len=96  # 使用96个历史点（约3个月的日数据）
target_col="代理购电"

echo "开始训练代理购电预测模型..."

# 1. 短期预测（预测未来1天）
echo "训练短期预测模型（1天）..."
python -u run.py \
  --task_name long_term_sg_forecast \
  --is_training 1 \
  --gpu $gpu \
  --batch_size 32 \
  --root_path $dataset \
  --data_path "代理购电.xlsx" \
  --model_id "proxy_power_1d" \
  --train_epochs 50 \
  --model $model_name \
  --data proxy_power \
  --features S \
  --target $target_col \
  --seq_len $seq_len \
  --label_len 24 \
  --pred_len 1 \
  --e_layers 2 \
  --d_layers 1 \
  --factor 3 \
  --enc_in 1 \
  --dec_in 1 \
  --c_out 1 \
  --d_model 512 \
  --n_heads 8 \
  --d_ff 2048 \
  --dropout 0.1 \
  --learning_rate 0.0001 \
  --des 'Exp' \
  --itr 1

# 2. 中期预测（预测未来7天）
echo "训练中期预测模型（7天）..."
python -u run.py \
  --task_name long_term_sg_forecast \
  --is_training 1 \
  --gpu $gpu \
  --batch_size 16 \
  --root_path $dataset \
  --data_path "代理购电.xlsx" \
  --model_id "proxy_power_7d" \
  --train_epochs 50 \
  --model $model_name \
  --data proxy_power \
  --features S \
  --target $target_col \
  --seq_len $seq_len \
  --label_len 24 \
  --pred_len 7 \
  --e_layers 2 \
  --d_layers 1 \
  --factor 3 \
  --enc_in 1 \
  --dec_in 1 \
  --c_out 1 \
  --d_model 512 \
  --n_heads 8 \
  --d_ff 2048 \
  --dropout 0.1 \
  --learning_rate 0.0001 \
  --des 'Exp' \
  --itr 1

# 3. 长期预测（预测未来30天）
echo "训练长期预测模型（30天）..."
python -u run.py \
  --task_name long_term_sg_forecast \
  --is_training 1 \
  --gpu $gpu \
  --batch_size 8 \
  --root_path $dataset \
  --data_path "代理购电.xlsx" \
  --model_id "proxy_power_30d" \
  --train_epochs 50 \
  --model $model_name \
  --data proxy_power \
  --features S \
  --target $target_col \
  --seq_len $seq_len \
  --label_len 24 \
  --pred_len 30 \
  --e_layers 2 \
  --d_layers 1 \
  --factor 3 \
  --enc_in 1 \
  --dec_in 1 \
  --c_out 1 \
  --d_model 512 \
  --n_heads 8 \
  --d_ff 2048 \
  --dropout 0.1 \
  --learning_rate 0.0001 \
  --des 'Exp' \
  --itr 1

echo "所有模型训练完成！"
