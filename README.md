# 电力预测系统使用指南

## 项目概述

本项目是基于PatchTST模型的电力预测系统，支持多目标变量、多特征模式的自动化配置，具有良好的解耦合性和扩展性。系统可以处理Excel格式的时间序列数据，并提供1天、7天和30天的预测功能。

### 主要特点

- **多目标变量支持**：可预测"代理购电"、"居民"、"农业"等多个目标变量
- **多特征模式**：支持单变量(S)、多变量(M)和多变量单输出(MS)三种模式
- **自动化配置**：根据目标变量和特征模式自动配置模型参数
- **可视化结果**：生成预测结果图表和CSV文件
- **集中化配置**：统一管理所有配置信息，便于维护和扩展

## 安装和环境配置

### 环境要求

- Python 3.7+
- PyTorch 1.8+
- CUDA支持（可选，用于GPU加速）

### 安装依赖

```bash
pip install -r electricity_predict/requirements.txt
```

或者单独安装关键包：

```bash
pip install torch pandas numpy matplotlib scikit-learn openpyxl
```

### 数据准备

确保 `./data/代理购电.xlsx` 文件存在，包含以下列：
- `日期`: 日期信息（格式：YYYYMMDD，如：20220501）
- `代理购电`: 目标预测列
- 其他列（可选）：`居民`, `农业` 等

## 使用方法

### 1. 训练模型

#### 使用统一训练脚本

```bash
# 训练所有目标变量的模型
python train_models.py

# 训练指定目标变量（默认单变量模式）
python train_models.py 农业

# 训练指定目标变量和特征模式
python train_models.py 农业 S
python train_models.py 居民 MS
python train_models.py 代理购电 M

# 显示帮助信息
python train_models.py help
```

#### 直接使用训练脚本

```bash
# 使用默认配置（农业，单变量）
python run_proxy_power.py

# 指定目标变量和特征模式
python run_proxy_power.py 居民 MS
```

### 2. 模型预测

#### 使用统一预测脚本

```bash
# 交互式选择可用模型进行预测
python predict_models.py

# 列出所有可用模型
python predict_models.py list

# 预测指定目标变量
python predict_models.py 农业
python predict_models.py 居民 MS

# 显示帮助信息
python predict_models.py help
```

#### 直接使用推理脚本

```bash
# 使用默认配置（农业，单变量）
python inference_proxy_power.py

# 指定目标变量和特征模式
python inference_proxy_power.py 居民 MS
```

#### 使用批处理文件（Windows）

```cmd
# 预测所有可用模型
predict_all.bat

# 预测农业模型
predict_agriculture.bat
```

## 支持的预测模式

### 预测长度

- **1天预测**：短期预测
- **7天预测**：中期预测
- **30天预测**：长期预测

### 目标变量

| 中文名称 | 英文标识 | 描述 |
|---------|---------|------|
| 代理购电 | proxy_power | 代理购电量预测 |
| 居民 | resident | 居民用电量预测 |
| 农业 | agriculture | 农业用电量预测 |

### 特征模式

| 模式 | 名称 | 描述 |
|-----|------|------|
| S | 单变量 | 单变量输入单变量输出 |
| M | 多变量 | 多变量输入多变量输出 |
| MS | 多变量单输出 | 多变量输入单变量输出 |

## 项目结构

```
├── checkpoints/                # 模型权重文件
├── data/                       # 数据文件
│   └── 代理购电.xlsx           # 主数据文件
├── data_provider/              # 数据加载模块
│   ├── __init__.py
│   ├── data_loader.py          # 通用数据加载器
│   └── sg_dataset.py           # 电力数据集类
├── electricity_predict/        # 核心预测模块
│   ├── requirements.txt        # 依赖包列表
│   └── 使用指南.md             # 详细使用说明
├── exp/                        # 实验模块
├── layers/                     # 模型层定义
├── models/                     # 模型定义
│   ├── __init__.py
│   └── PatchTST.py             # PatchTST模型实现
├── results/                    # 结果保存目录
│   └── {model_name}/           # 每个模型的结果
│       ├── metrics_{model_id}.csv  # 评估指标(CSV格式)
│       ├── metrics.npy         # 评估指标(兼容格式)
│       ├── test_pred.npy       # 测试预测结果
│       └── test_true.npy       # 测试真实值
├── test_results/               # 测试结果和可视化
│   └── {model_name}/           # 每个模型的测试结果
│       └── *.pdf               # 可视化图表(显示真实数值)
├── scripts/                    # 脚本文件
├── utils/                      # 工具函数
├── config_manager.py           # 配置管理器
├── inference_proxy_power.py    # 推理脚本
├── predict_models.py           # 统一预测脚本
├── run_proxy_power.py          # 训练脚本
├── train_models.py             # 统一训练脚本
└── README.md                   # 本文档
```

## 输出文件说明

### 训练和测试输出

- **模型权重**: `./checkpoints/{model_name}/checkpoint.pth`
- **评估指标CSV**: `./results/{model_name}/metrics_{model_id}.csv`
- **可视化图表**: `./test_results/{model_name}/*.pdf` (显示反标准化后的真实数值)
- **预测结果**: `prediction_{model_id}.csv` 和 `prediction_{model_id}.png`

### 评估指标CSV格式

新的CSV格式包含以下指标：

```csv
Metric,Value
MAE,0.26366752
MSE,0.120798305
RMSE,0.3475605
MAPE,0.38790217
MSPE,1.3409436
```

这种格式便于：
- 数据分析和比较
- 导入到Excel或其他分析工具
- 自动化报告生成

## 配置说明

### 自动生成的文件名

系统会根据配置自动生成文件名：

- **模型ID**: `{target_english_id}_{pred_len}d_{features_suffix}`
- **预测结果CSV**: `prediction_{model_id}.csv`
- **预测图表PNG**: `prediction_{model_id}.png`
- **模型权重目录**: `long_term_sg_forecast_{model_id}_PatchTST_{target_english_id}`

示例：
- 农业7天预测单变量模式：`agriculture_7d`
- 居民1天预测多变量单输出模式：`resident_1d_ms`

### 扩展新目标变量

要添加新的目标变量，只需在 `config_manager.py` 中的 `TARGET_CONFIGS` 字典中添加配置：

```python
TARGET_CONFIGS = {
    # 现有配置...
    '工业': TargetConfig('工业', 'industry', '工业用电量预测'),
    '商业': TargetConfig('商业', 'commercial', '商业用电量预测'),
}
```


