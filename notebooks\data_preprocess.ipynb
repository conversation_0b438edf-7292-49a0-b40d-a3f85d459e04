{"cells": [{"cell_type": "code", "execution_count": 29, "id": "bbe7f85c0106a56b", "metadata": {"jupyter": {"is_executing": true}}, "outputs": [], "source": ["import pandas as pd\n", "import torch\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "id": "fa1f8d0fb3a3ca61", "metadata": {}, "outputs": [], "source": ["import os"]}, {"cell_type": "code", "execution_count": 4, "id": "e0fa396d-059f-43b6-acbe-ffa9c9b302ae", "metadata": {}, "outputs": [{"data": {"text/plain": ["['.idea',\n", " 'checkpoints',\n", " 'data_provider',\n", " 'exp',\n", " 'inference.py',\n", " 'layers',\n", " 'models',\n", " 'notebooks',\n", " 'requirements.txt',\n", " 'results',\n", " 'result_long_term_forecast.txt',\n", " 'run.py',\n", " 'scripts',\n", " 'test_results',\n", " 'utils',\n", " '近3天96点负荷.xls']"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["os.listdir('..')"]}, {"cell_type": "code", "execution_count": 5, "id": "8846b3ac-663b-4bb5-830c-d15485adde9c", "metadata": {}, "outputs": [], "source": ["data_path = '../近3天96点负荷.xls'"]}, {"cell_type": "code", "execution_count": 11, "id": "43fad5e2-3446-45c1-9f1e-25aad4543475", "metadata": {}, "outputs": [], "source": ["df = pd.read_excel(os.path.join(data_path))"]}, {"cell_type": "code", "execution_count": 12, "id": "99157585-af2e-434a-82b8-9643bd6c9820", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>户号</th>\n", "      <th>时间点</th>\n", "      <th>正向有功总</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3309937392688</td>\n", "      <td>2025-05-13 00:00:00</td>\n", "      <td>3812.52</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3309937392688</td>\n", "      <td>2025-05-13 00:15:00</td>\n", "      <td>3812.52</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3309937392688</td>\n", "      <td>2025-05-13 00:30:00</td>\n", "      <td>3812.52</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3309937392688</td>\n", "      <td>2025-05-13 00:45:00</td>\n", "      <td>3812.52</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3309937392688</td>\n", "      <td>2025-05-13 01:00:00</td>\n", "      <td>3812.52</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>283</th>\n", "      <td>3309937392688</td>\n", "      <td>2025-05-15 22:45:00</td>\n", "      <td>3851.72</td>\n", "    </tr>\n", "    <tr>\n", "      <th>284</th>\n", "      <td>3309937392688</td>\n", "      <td>2025-05-15 23:00:00</td>\n", "      <td>3851.72</td>\n", "    </tr>\n", "    <tr>\n", "      <th>285</th>\n", "      <td>3309937392688</td>\n", "      <td>2025-05-15 23:15:00</td>\n", "      <td>3851.72</td>\n", "    </tr>\n", "    <tr>\n", "      <th>286</th>\n", "      <td>3309937392688</td>\n", "      <td>2025-05-15 23:30:00</td>\n", "      <td>3851.72</td>\n", "    </tr>\n", "    <tr>\n", "      <th>287</th>\n", "      <td>3309937392688</td>\n", "      <td>2025-05-15 23:45:00</td>\n", "      <td>3851.72</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>288 rows × 3 columns</p>\n", "</div>"], "text/plain": ["                户号                 时间点    正向有功总\n", "0    3309937392688 2025-05-13 00:00:00  3812.52\n", "1    3309937392688 2025-05-13 00:15:00  3812.52\n", "2    3309937392688 2025-05-13 00:30:00  3812.52\n", "3    3309937392688 2025-05-13 00:45:00  3812.52\n", "4    3309937392688 2025-05-13 01:00:00  3812.52\n", "..             ...                 ...      ...\n", "283  3309937392688 2025-05-15 22:45:00  3851.72\n", "284  3309937392688 2025-05-15 23:00:00  3851.72\n", "285  3309937392688 2025-05-15 23:15:00  3851.72\n", "286  3309937392688 2025-05-15 23:30:00  3851.72\n", "287  3309937392688 2025-05-15 23:45:00  3851.72\n", "\n", "[288 rows x 3 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 13, "id": "2bb94df3-590b-4fb3-a245-f1b2b67bfe65", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 15, "id": "4888c68c-af6f-4dae-b271-649a3784085d", "metadata": {}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x1f5525026d0>]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjsAAAGdCAYAAAD0e7I1AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy88F64QAAAACXBIWXMAAA9hAAAPYQGoP6dpAABK4klEQVR4nO3deXxU9b3/8ddkmxDCTCABAiSsA0QiYYmCoYiIoEDLIlWsWoItIlpr70/aItyKS6sFBfWickNba81tpQGFREQQggpi2WMiASQ17EJCgJidTJY5vz8iUyIgZD3J5P18PM6DmTlnvvM5XyaZd875zvlaDMMwEBEREfFQXmYXICIiItKQFHZERETEoynsiIiIiEdT2BERERGPprAjIiIiHk1hR0RERDyawo6IiIh4NIUdERER8Wg+ZhfQUFwuF6dOnaJNmzZYLBazyxEREZFrYBgGhYWFdO7cGS+v+jkm47Fh59SpU4SHh5tdhoiIiNTCiRMnCAsLq5e2PDbstGnTBqjqLJvNZnI1IiIici0KCgoIDw93f47XB48NOxdOXdlsNoUdERGRZqY+h6BogLKIiIh4NIUdERER8WgKOyIiIuLRFHZERETEoynsiIiIiEdT2BERERGPprAjIiIiHk1hR0RERDyawo6IiIh4NIUdERER8WgKOyIiIuLRFHZERETEo3nsRKAiIiLNSXmli30n88nKL6W0vJLSchfnyyspLa/EMAwMo2o7Ay66XXXDMPj21n9WXm27ausuartq3fdvf+H26Os6Mrx3SD3sfcNS2BERETFJdn4p76Wd5LPMs6Qc+4aSskqzS6qRDjarwo6IiIhUV+ysYHPGGd5NOcGWf5/BZfxnnb2VL707BNLKzxt/X29a+Xpj9fHCx9vy7RZV/1osF25V3a5aY7no9oV1F25dbbv/bOt+xtW2x8Lgrm1r1wmNTGFHRESkgeWXlLPmi5Mkf5nDjkPnKKt0udfd2L0t4/t34qaewfTt2AYvL8v3tCS1obAjIiLSQI6fK+GNzw7zbsrX1U5RdQsO4If9O3FXdBg92weaWGHLoLAjIiJSjwzDYO/X+bzx2RE+2HvKfZoqIrQNkwd1YfR1HejVPrDaKSZpWAo7IiIi9eBckZPlO4/z7udfc+xcifvxEX3aM2tET4b1ClbAMYnCjoiISB19fPA0j76dyvnyqlNVVh8vxl4fykMjehLZ2W5ydaKwIyIiUgfJB07zi7dTKK80uL6LjZ//oAd3RIbS2qqP2KZC/xMiIiK1dPhMEb/6ZyrllQY/jOrE/9wzEF9vTU7Q1CjsiIiI1EJ5pYvHV6RxvrySYb2CWXLPQHwUdJok/a+IiIjUwqsffcUXX+dj8/fhpakDFHSasBr9z8TFxREVFYXNZsNmsxETE8P69evd67Ozs5k2bRqhoaG0bt2awYMHs2rVqmptdO/eHYvFUm1ZuHBhtW327t3LzTffjL+/P+Hh4bz44ot12EUREZH6tedoLks/yQTgj1P608neyuSK5PvU6DRWWFgYCxcupHfv3hiGQXx8PJMmTSI1NZXIyEhiY2PJy8tjzZo1hISEsHz5cqZOncqePXsYNGiQu53f//73zJw5032/TZs27tsFBQXcfvvtjB49mmXLlpGens7Pf/5zgoKCeOihh+phl0VERGrvfFklj69Mw2XAlMFd+FFUZ7NLkquoUdiZMGFCtfvPP/88cXFx7Nixg8jISLZt20ZcXBxDhgwB4Mknn+SVV14hJSWlWthp06YNoaGhl32Nt99+m7KyMt588038/PyIjIwkLS2Nl19+WWFHRERM99rHX3Ei9zyd7f48MzHS7HLkGtT6BGNlZSUJCQkUFxcTExMDwLBhw1ixYgW5ubm4XC4SEhIoLS1l5MiR1Z67cOFCgoODGTRoEIsWLaKiosK9bvv27YwYMQI/Pz/3Y3fccQcZGRl88803V6zH6XRSUFBQbREREalPmTmF/GXrYQCemRiJzd/X5IrkWtT421jp6enExMRQWlpKYGAgiYmJ9OvXD4CVK1dyzz33EBwcjI+PDwEBASQmJuJwONzP/9WvfsXgwYNp164d27ZtY968eWRlZfHyyy8DVeN+evToUe01O3bs6F7Xtu3lZ1hdsGABzz77bE13R0RE5Jq9nPxvyisNbovowJh+Hc0uR65RjcNO3759SUtLIz8/n3fffZfp06ezZcsW+vXrx/z588nLy2PTpk2EhISQlJTE1KlT2bp1K/379wdg9uzZ7raioqLw8/Nj1qxZLFiwAKvVWusdmTdvXrW2CwoKCA8Pr3V7IiIiFzuVd54N+08D8NuxfTX1QzNS47Dj5+fnPlITHR3N7t27WbJkCXPmzOH1119n3759REZWncMcMGAAW7duZenSpSxbtuyy7Q0dOpSKigqOHj1K3759CQ0N5fTp09W2uXD/SuN8AKxWa53CkoiIyPf5x45jVLoMburZjohQm9nlSA3U+aIALpcLp9NJSUnVpGdeXtWb9Pb2xuVyXfH5aWlpeHl50aFDBwBiYmL49NNPKS8vd2+TnJxM3759r3gKS0REpCGVlFWQsPsEAA8M625uMVJjNTqyM2/ePMaNG0fXrl0pLCxk+fLlbN68mQ0bNhAREYHD4WDWrFksXryY4OBgkpKSSE5OZu3atUDV4OOdO3dy66230qZNG7Zv387jjz/OT3/6U3eQue+++3j22WeZMWMGTzzxBPv27WPJkiW88sor9b/3IiIi1+DZNQfILS4jrG0rRl+nsTrNTY3CTk5ODrGxsWRlZWG324mKimLDhg2MGTMGgHXr1jF37lwmTJhAUVERDoeD+Ph4xo8fD1SdakpISOCZZ57B6XTSo0cPHn/88Wpjbex2Oxs3buTRRx8lOjqakJAQnnrqKX3tXERETLHmi1Os2HMCiwVevCtKV0puhiyGYRhmF9EQCgoKsNvt5OfnY7Pp3KqIiNRcTmEpY17+lPzz5Tw2ysGvb+9rdkkeryE+vxVPRUREruCppP3kny/n+i42/uu23maXI7WksCMiInIZn311lg/3Z+PjZeHFH2uiz+ZM/3MiIiKXsXzXMQDuG9qVfp01HKI5U9gRERH5jrNFTpIPVF3j7d4hXU2uRupKYUdEROQ7Ej8/SXmlwYAwO9d10lGd5k5hR0RE5CKVLoN/7joOwD036qiOJ1DYERERuciH+7I5fLYYm78PEwd2NrscqQcKOyIiIt8yDIPXP8kE4IEf9CDQWuMpJKUJUtgRERH5VvKB03yZVUBrP29+pjmwPIbCjoiICFBaXslzH3wJQOyw7rRt7WdyRVJfFHZERESA//0kk+O5JYTa/PnlrQ6zy5F6pLAjIiIt3uEzRSzbchiApyf0o7XG6ngUhR0REWnRDMPgqff2U1bp4pY+7Rl7fajZJUk9U9gREZEWbc0Xp/gs8yx+Pl78flIkFovF7JKkninsiIhIi3XkbDFPJu4D4NGRDroFtza5ImkICjsiItIiFTkr+MXbn1PorGBI93b84tZeZpckDURhR0REWpzS8koejN/Nl1kFhAT68dp9g/D11keip9L/rIiItChlFS4e+UcKOw7nEmj14a/Tb6Sjzd/ssqQBKeyIiEiLUVHp4vEVaXyScQZ/Xy/+Ov0GBoQHmV2WNDCFHRERaRFcLoO5q9P5ID0LX28Lf5p2A0N7BptdljQChR0REWkRXvjwIO+mfI23l4XX7h3ELX3am12SNBKFHRER8XjHz5Xwl61VV0hedFcUY6/vZHJF0pgUdkRExOP9ZethXAaM6NOeKYPDzC5HGpnCjoiIeLQzhU5W7jkBwCO36Fo6LZHCjoiIeCzDMHj+gwM4K1wMDA/ipp7tzC5JTKCwIyIiHmvV5ydJSjuFt5eF+T+6TvNetVAKOyIi4pH+lXmW3yWmAzB7TB+iu+moTkulsCMiIh7nX5ln+flbu3FWuBh9XQce1lidFk1hR0REPMrFQWdURAeW3j8Yby+dvmrJFHZERMRjfDfoxP10MFYfb7PLEpMp7IiIiEfYpqAjV6CwIyIizV5mTiGz/p6ioCOXVaOwExcXR1RUFDabDZvNRkxMDOvXr3evz87OZtq0aYSGhtK6dWsGDx7MqlWrLtuW0+lk4MCBWCwW0tLS3I8fPXoUi8VyybJjx47a7aGIiHi0nMJSZsTvodBZwZDu7RR05BI1CjthYWEsXLiQlJQU9uzZw6hRo5g0aRL79+8HIDY2loyMDNasWUN6ejpTpkxh6tSppKamXtLWnDlz6Ny58xVfa9OmTWRlZbmX6OjoGu6aiIh4uhO5Jdy9bDvHzpUQ1raVgo5cVo3CzoQJExg/fjy9e/emT58+PP/88wQGBrqPumzbto3HHnuMIUOG0LNnT5588kmCgoJISUmp1s769evZuHEjixcvvuJrBQcHExoa6l58fX1rsXsiIuKpzhY5ufcvO9xB5x8zhhIcaDW7LGmCaj1mp7KykoSEBIqLi4mJiQFg2LBhrFixgtzcXFwuFwkJCZSWljJy5Ej3806fPs3MmTP5+9//TkBAwBXbnzhxIh06dGD48OGsWbPmqvU4nU4KCgqqLSIi4pmcFZU88o8Uvv7mPN2CA3j34WF0D2ltdlnSRNU47KSnpxMYGIjVauXhhx8mMTGRfv36AbBy5UrKy8sJDg7GarUya9YsEhMTcTgcQNUcJQ888AAPP/wwN9xww2XbDwwM5KWXXuKdd97hgw8+YPjw4UyePPmqgWfBggXY7Xb3Eh4eXtNdExGRZiCvpIzpb+5i99FvaOPvw1+n30io3d/ssqQJsxiGYdTkCWVlZRw/fpz8/Hzeffdd3njjDbZs2UK/fv147LHH2LVrF3/84x8JCQkhKSmJV155ha1bt9K/f39effVVVq5cyZYtW/D29ubo0aP06NGD1NRUBg4ceMXXjI2N5ciRI2zduvWK2zidTpxOp/t+QUEB4eHh5OfnY7PZarKLIiLSRB0+U8SM+D0cOVtMoNWHP0+LZpgjxOyypB4VFBRgt9vr9fO7xmHnu0aPHk2vXr2YM2cODoeDffv2ERkZWW29w+Fg2bJlTJ48mffff7/aRGyVlZV4e3tz//33Ex8ff9nXWLp0Kc899xxZWVnXXFdDdJaIiJjn44OneXzFF+SfL6dLUCvefOBG+oa2MbssqWcN8fntU9cGXC4XTqeTkpISALy8qp8Z8/b2xuVyAfDqq6/y3HPPudedOnWKO+64gxUrVjB06NArvkZaWhqdOnWqa6kiItIMfVNcxrPv7ycp7RQAg7oG8edpN9C+jQYjy7WpUdiZN28e48aNo2vXrhQWFrJ8+XI2b97Mhg0biIiIwOFwMGvWLBYvXkxwcDBJSUkkJyezdu1aALp27VqtvcDAQAB69epFWFgYAPHx8fj5+TFo0CAAVq9ezZtvvskbb7xR550VEZHmo7S8ktWfn+Tl5AzOFpXhZYEHb+7J7DF98PfV18vl2tUo7OTk5BAbG0tWVhZ2u52oqCg2bNjAmDFjAFi3bh1z585lwoQJFBUV4XA4iI+PZ/z48TUq6g9/+APHjh3Dx8eHiIgIVqxYwV133VWjNkREpHkyDIPE1JM898GX5BaXAdCnYyAv3jWAgeFB5hYnzVKdx+w0VRqzIyLS/JSUVTB7xRd8uD8bgLC2rXhgWHemxXTTxQJbiCY5ZkdERKQ+nCl08mD8br74Oh8/by/+a3RvHhrRE19vTeModaOwIyIipsvILuTB/9vNidzztA3w5Y3pNxDdrZ3ZZYmHUNgREZFGVVHp4ui5EnYfzXUvJ3LPA9AtOIC3fjaEHroastQjhR0RkTqodBlk5hRx5GwRR86WcPRsMUfOFZNfUu7exuA/QyMvHiV58YDJ7w6fNK5w50rP+e7gy+qvc4XX/54Rm1dq+0rtXrruCo8bBt+UlOH6zmt7WeDm3u15eeoAzW8l9U5hR0SkFk4XlPL6x5ms35fF2aIys8tpdqw+XgwMD+LG7u24sUc7BncNoo2/JnyWhqGwIyJSA4Zh8I+dx1m47kuKyyoBCLT60Kt9a3qEtKZ7SNW/IYFWLBc/0XLxzf/csVguu8m3666+neW7T7qGtqu1e8lrXr3OK23/fc+5+Ha7AD+CA614e12hUZF6prAjInKNyipczE/ax4o9JwAYGB7E42P6MKxXsL4xJNKEKeyIiFyD8koXv1z+ORsPnMbLAnPHRfDg8J546eiESJOnsCMichUul8GvV37BxgOn8fPxYtlPBzMqoqPZZYnINdJxVxGRq/ifTf9mzRen8PW2KOiINEMKOyIi3yMp9SSvfpwJwB/v7K+gI9IMKeyIiFzB5owcfvPOFwDMGtGTu28IN7kiEakNhR0Rkcv46nQhj/zjcypcBhMHdOaJsRFmlyQitaSwIyLyHaXllfxyeSrnyysZ1iuYxXcP0LeuRJoxhR0Rke944cODZJwuJCTQypKfDMLPR78qRZoz/QSLiFzkwKkC4rcdBeClqQNo30bzNIk0dwo7IiLfMgyDZ9bsx2XAD6M6cUuf9maXJCL1QGFHRORba744xa6jubTy9eZ3468zuxwRqScKOyIiQJGzguc/+BKAX45y0DmolckViUh9UdgREQFe+/grcgqddAsO4MGbe5hdjojUI4UdEWnxTuad52+fHQXgqR/1w+rjbW5BIlKvFHZEpMV77aOvKKt0MbRHO0ZFdDC7HBGpZwo7ItKiHT5TxDspXwMwZ2xfLBZdPFDE0yjsiEiL9vonmVS6DEZFdCC6WzuzyxGRBqCwIyIt1oncEt5LOwXAf93W2+RqRKShKOyISIsVt+UQlS6Dm3uHMCA8yOxyRKSBKOyISIuUU1DKu3uqxur88laHydWISENS2BGRFumtbUcpq3QxuGsQQ3sGm12OiDQghR0RaXGKnBX8fccxAGbd0svkakSkoSnsiEiLk7DrOIWlFfQMac2Y6zqaXY6INDCFHRFpUcorXbz52REAZo7oiZeXrqsj4ukUdkSkRflgbxan8ksJCbRy56AuZpcjIo2gRmEnLi6OqKgobDYbNpuNmJgY1q9f716fnZ3NtGnTCA0NpXXr1gwePJhVq1Zdti2n08nAgQOxWCykpaVVW7d3715uvvlm/P39CQ8P58UXX6z5nomIfIdhGCzbcgiAn/2gO/6+mgNLpCWoUdgJCwtj4cKFpKSksGfPHkaNGsWkSZPYv38/ALGxsWRkZLBmzRrS09OZMmUKU6dOJTU19ZK25syZQ+fOnS95vKCggNtvv51u3bqRkpLCokWLeOaZZ/jzn/9cy10UEamy/fA5DmYXEuDnzU+HdjO7HBFpJDUKOxMmTGD8+PH07t2bPn368PzzzxMYGMiOHTsA2LZtG4899hhDhgyhZ8+ePPnkkwQFBZGSklKtnfXr17Nx40YWL158yWu8/fbblJWV8eabbxIZGclPfvITfvWrX/Hyyy/XYTdFROD/tlV9A+vHg8OwB/iaXI2INJZaj9mprKwkISGB4uJiYmJiABg2bBgrVqwgNzcXl8tFQkICpaWljBw50v2806dPM3PmTP7+978TEBBwSbvbt29nxIgR+Pn5uR+74447yMjI4JtvvrliPU6nk4KCgmqLiMgFJ/POs/FANgCxMTqqI9KS1DjspKenExgYiNVq5eGHHyYxMZF+/foBsHLlSsrLywkODsZqtTJr1iwSExNxOKquTmoYBg888AAPP/wwN9xww2Xbz87OpmPH6l8FvXA/Ozv7inUtWLAAu93uXsLDw2u6ayLiwZbvPIbLgJiewfTu2MbsckSkEdU47PTt25e0tDR27tzJI488wvTp0zlw4AAA8+fPJy8vj02bNrFnzx5mz57N1KlTSU9PB+C1116jsLCQefPm1e9eAPPmzSM/P9+9nDhxot5fQ0Sap9LySv65q+p3wvRhOqoj0tL41PQJfn5+7iM10dHR7N69myVLljBnzhxef/119u3bR2RkJAADBgxg69atLF26lGXLlvHxxx+zfft2rFZrtTZvuOEG7r//fuLj4wkNDeX06dPV1l+4HxoaesW6rFbrJe2KiACsS88it7iMTnZ/RusigiItTp2vs+NyuXA6nZSUlFQ16FW9SW9vb1wuFwCvvvoqX3zxBWlpaaSlpbFu3ToAVqxYwfPPPw9ATEwMn376KeXl5e42kpOT6du3L23btq1ruSLSAv3f9qqByT+9qRs+3rq8mEhLU6MjO/PmzWPcuHF07dqVwsJCli9fzubNm9mwYQMRERE4HA5mzZrF4sWLCQ4OJikpieTkZNauXQtA165dq7UXGBgIQK9evQgLCwPgvvvu49lnn2XGjBk88cQT7Nu3jyVLlvDKK6/Ux/6KSAuz9+s80k7k4eftxT03aiyfSEtUo7CTk5NDbGwsWVlZ2O12oqKi2LBhA2PGjAFg3bp1zJ07lwkTJlBUVITD4SA+Pp7x48df82vY7XY2btzIo48+SnR0NCEhITz11FM89NBDNdszERHgn7uOAzC+fyghgTrVLdISWQzDMMwuoiEUFBRgt9vJz8/HZrOZXY6ImKDIWcHQ5zdRXFbJioduYmjPYLNLEpGraIjPb528FhGP9f4Xpyguq6Rn+9YM6dHO7HJExCQKOyLisRK+PYX1kxvDsVg0u7lIS6WwIyIe6cCpAr74Oh9fbws/HhxmdjkiYiKFHRHxSAm7q47q3B4ZSrAGJou0aAo7IuJxzpdVkph6EoB7b+x6la1FxNMp7IiIx/kgPYvC0grC27ViWC99A0ukpVPYERGP85+ByV3x8tLAZJGWTmFHRDzKV6cL2XPsG7y9LNwdrYHJIqKwIyIe5sLs5rdFdKCDzd/kakSkKVDYERGP4ayoZHXq1wDcO0QDk0WkisKOiHiMTw7mkFdSTqjNnxF92ptdjog0EQo7IuIxVn1e9XXzyYO64K2BySLyLYUdEfEIucVlfHIwB4Apg7uYXI2INCUKOyLiEd7/4hQVLoP+Xez06djG7HJEpAlR2BERj7D686qByTqqIyLfpbAjIs1eZk4hX3ydj4+XhQkDOptdjog0MQo7ItLsXRiYPLJve0I06aeIfIfCjog0a5Uug6RvJ/388WBdMVlELqWwIyLN2o7D58jKL8Xm78Oo6zqYXY6INEEKOyLSrK36dmDyhAGdsfp4m1yNiDRFCjsi0mwVOyv4cF82AFN0CktErkBhR0SarQ/3ZVNSVkn34AAGdw0yuxwRaaIUdkSk2bow6eeUwWFYLJoeQkQuT2FHRJqlU3nn2XboHAB3DtKFBEXkyhR2RKRZSko7iWHA0B7tCG8XYHY5ItKEKeyISLNjGAarP9e1dUTk2ijsiEizs/frfDJzirD6eDGuf6jZ5YhIE6ewIyLNzoVJP++IDKWNv6/J1YhIU6ewIyLNSlmFizVfnAI0w7mIXBuFHRFpVjZn5PBNSTnt21gZ7ggxuxwRaQYUdkSkWbkwMPnOQV3w8davMBG5Ov2mEJFmI6+kjI8OngZ0CktErl2Nwk5cXBxRUVHYbDZsNhsxMTGsX7/evT47O5tp06YRGhpK69atGTx4MKtWrarWxsSJE+natSv+/v506tSJadOmcerUKff6o0ePYrFYLll27NhRx10Vkebu/S9OUV5p0K+TjYhQm9nliEgzUaOwExYWxsKFC0lJSWHPnj2MGjWKSZMmsX//fgBiY2PJyMhgzZo1pKenM2XKFKZOnUpqaqq7jVtvvZWVK1eSkZHBqlWrOHToEHfdddclr7Vp0yaysrLcS3R0dB13VUSau1XfnsLSUR0RqQmLYRhGXRpo164dixYtYsaMGQQGBhIXF8e0adPc64ODg3nhhRd48MEHL/v8NWvWMHnyZJxOJ76+vhw9epQePXqQmprKwIEDa11XQUEBdrud/Px8bDb9BSjS3B06U8RtL23B28vC9nmj6NDG3+ySRKQBNMTnd63H7FRWVpKQkEBxcTExMTEADBs2jBUrVpCbm4vL5SIhIYHS0lJGjhx52TZyc3N5++23GTZsGL6+1a+VMXHiRDp06MDw4cNZs2bNVetxOp0UFBRUW0TEcyR+e1Tn5t4hCjoiUiM1Djvp6ekEBgZitVp5+OGHSUxMpF+/fgCsXLmS8vJygoODsVqtzJo1i8TERBwOR7U2nnjiCVq3bk1wcDDHjx/nvffec68LDAzkpZde4p133uGDDz5g+PDhTJ48+aqBZ8GCBdjtdvcSHh5e010TkSbK5TJITL1wCkvTQ4hIzdT4NFZZWRnHjx8nPz+fd999lzfeeIMtW7bQr18/HnvsMXbt2sUf//hHQkJCSEpK4pVXXmHr1q3079/f3cbZs2fJzc3l2LFjPPvss9jtdtauXYvFYrnsa8bGxnLkyBG2bt16xbqcTidOp9N9v6CggPDwcJ3GEvEAOw+f454/7yDQ6sOeJ0fj7+ttdkki0kAa4jRWncfsjB49ml69ejFnzhwcDgf79u0jMjKy2nqHw8GyZcsu+/yvv/6a8PBwtm3b5j4d9l1Lly7lueeeIysr65rr0pgdEc8xd9VeEnaf4O7oMBbdPcDsckSkATWpMTsXuFwunE4nJSUlVQ16VW/S29sbl8v1vc8Hqh2V+a60tDQ6depU11JFpBkqLa/kg/SqP3Tu1LewRKQWfGqy8bx58xg3bhxdu3alsLCQ5cuXs3nzZjZs2EBERAQOh4NZs2axePFigoODSUpKIjk5mbVr1wKwc+dOdu/ezfDhw2nbti2HDh1i/vz59OrVy31UJz4+Hj8/PwYNGgTA6tWrefPNN3njjTfqeddFpDn4+GAOhaUVdLb7c1OPYLPLEZFmqEZhJycnh9jYWLKysrDb7URFRbFhwwbGjBkDwLp165g7dy4TJkygqKgIh8NBfHw848ePByAgIIDVq1fz9NNPU1xcTKdOnRg7dixPPvkkVqvV/Tp/+MMfOHbsGD4+PkRERLBixYrLXotHRDzfhekhJg3qgpfX5cf1iYh8nzqP2WmqNGZHpPnLLS5jyPObqHAZJD8+gt4d25hdkog0sCY5ZkdEpKGs3XuKCpfB9V1sCjoiUmsKOyLSZL2b8jUAdw7StXVEpPYUdkSkScrILmTv1/n4eFmYPLCz2eWISDOmsCMiTdK7KScAGBXRgeBA61W2FhG5MoUdEWlyyitdJKaeAuDuGzT1i4jUjcKOiDQ5WzLOcLbISUigHyP7tje7HBFp5hR2RKTJuTAwefLALvh669eUiNSNfouISJOSW1zGRwdPA3DXDfoWlojUncKOiDQpSaknKa806N/FTkSoLggqInWnsCMiTcqFU1h366iOiNQThR0RaTL2n8rnQFYBft5eTByga+uISP1Q2BGRJuPCUZ0x/ToSFOBncjUi4ikUdkSkSSircPFeWtW1de6K1iksEak/Cjsi0iR8fDCH3OIyOrSxcnPvELPLEREPorAjIk3Chekh7hzcBR9dW0dE6pF+o4iI6XIKS/kk4wwAd+sUlojUM4UdETHde6mnqHQZDAwPwtGhjdnliIiHUdgREVMZhsE7357C0rV1RKQhKOyIiKnST+bz79NFWH28+FGUrq0jIvVPYUdETPXOnqpr69wRGYq9la/J1YiIJ1LYERHTlJZXsuYLXVtHRBqWwo6ImGb9vizyz5fTye7PDxy6to6INAyFHRExRWl5JYs3/BuA+4d2xdvLYnJFIuKpFHZExBR/+fQwJ/PO08nuz4zhPc0uR0Q8mMKOiDS67PxS/nfzIQDmjouglZ+3yRWJiCdT2BGRRvfihoOcL69kcNcgJg7Q181FpGEp7IhIo/riRB6rPz8JwFMTIrFYNFZHRBqWwo6INKolH30FwJRBXRgYHmRuMSLSIijsiEijycwp5OODOVgs8Nhtvc0uR0RaCIUdEWk0f/3sKACjr+tIj5DW5hYjIi2Gwo6INIqcglJWf141NcTMm/VVcxFpPDUKO3FxcURFRWGz2bDZbMTExLB+/Xr3+uzsbKZNm0ZoaCitW7dm8ODBrFq1qlobEydOpGvXrvj7+9OpUyemTZvGqVOnqm2zd+9ebr75Zvz9/QkPD+fFF1+swy6KSFOweGMGzgoXg7sGcWP3tmaXIyItSI3CTlhYGAsXLiQlJYU9e/YwatQoJk2axP79+wGIjY0lIyODNWvWkJ6ezpQpU5g6dSqpqanuNm699VZWrlxJRkYGq1at4tChQ9x1113u9QUFBdx+++1069aNlJQUFi1axDPPPMOf//znetplEWls+07m805K1VGdJ3/UT9/AEpFGZTEMw6hLA+*****************************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(df['正向有功总'])"]}, {"cell_type": "code", "execution_count": 22, "id": "d0b671ff-13b7-455f-90d8-83400bb47d22", "metadata": {}, "outputs": [], "source": ["X = torch.from_numpy(df['正向有功总'].values)\n", "torch.save(X, '../seq_x.pt')"]}, {"cell_type": "code", "execution_count": 21, "id": "12eefbb9-bed7-4f53-a58d-6f2b94efc858", "metadata": {}, "outputs": [{"data": {"text/plain": ["<PERSON>.<PERSON><PERSON>([288])"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["X.shape"]}, {"cell_type": "code", "execution_count": 25, "id": "722aa8b0-dcab-4092-a005-e781debb638c", "metadata": {}, "outputs": [], "source": ["prediction = pd.read_csv('../prediction_0.csv')['prediction']"]}, {"cell_type": "code", "execution_count": 27, "id": "0dfe11b3-6e7f-47fb-881c-9eea02ec128f", "metadata": {}, "outputs": [], "source": ["lookback = df['正向有功总']"]}, {"cell_type": "code", "execution_count": 30, "id": "403ce277-049f-49bb-aae6-d697b4e5425f", "metadata": {}, "outputs": [], "source": ["seq = np.concatenate([lookback, prediction])"]}, {"cell_type": "code", "execution_count": 31, "id": "78fd8f66-4426-40e4-96ba-ad1adef35e53", "metadata": {}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x1f55ea5ce80>]"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(seq)"]}, {"cell_type": "code", "execution_count": null, "id": "0b77b14d-21e7-4444-af9b-d17685b95857", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 5}