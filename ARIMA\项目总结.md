# 时间序列月度电力预测项目总结

## 项目概述

本项目成功在ARIMA文件夹下创建了一个完整的时间序列预测系统，支持ARIMA（自回归积分滑动平均）和SARIMAX（季节性ARIMA）两种模型，用于对月级别的电力数据进行时间序列预测。该系统支持对代理购电、居民用电、农业用电等多种电力消费类型进行月度预测分析。

## 项目结构

```
ARIMA/
├── arima_monthly_predictor.py    # 核心时间序列预测器类（支持ARIMA和SARIMAX）
├── example_usage.py              # 使用示例脚本
├── test_arima.py                 # ARIMA模型测试脚本
├── test_sarimax.py               # SARIMAX模型测试脚本
├── run_arima_prediction.py       # 快速运行脚本
├── requirements.txt              # 依赖包列表
├── README.md                     # 详细使用说明
├── 项目总结.md                   # 本文件
└── results/                      # 结果输出目录
    ├── *.csv                     # 预测结果CSV文件
    ├── *.png                     # 分析图表
    └── ...
```

## 核心功能

### 1. 数据处理
- 支持CSV和Excel格式的电力数据
- 自动将日度数据聚合为月度数据，或直接处理月度数据
- 支持多种目标变量（代理购电、居民、农业）
- 智能数据格式识别和转换

### 2. 时间序列分析
- **平稳性检验**: 使用ADF检验确保数据平稳性
- **季节性分解**: 分析趋势、季节性和残差成分
- **序列分析**: 生成ACF、PACF图表

### 3. 双模型支持
- **ARIMA模型**: 适用于非季节性时间序列数据
- **SARIMAX模型**: 适用于具有季节性特征的时间序列数据
- **自动参数选择**: 通过网格搜索找到最优参数组合
- **模型拟合**: 使用statsmodels库拟合模型
- **模型诊断**: 残差分析、Ljung-Box检验

### 4. 预测与评估
- **未来预测**: 支持任意月数的未来预测
- **置信区间**: 提供预测的置信区间
- **模型评估**: 使用历史数据进行回测评估

### 5. 可视化输出
- 时间序列分析图
- 季节性分解图
- 模型诊断图
- 预测结果图表

## 测试结果

### ARIMA模型测试
✅ **模块导入**: ARIMA模块成功导入
✅ **数据加载**: 成功加载42个月的代理购电数据
✅ **平稳性检验**: 数据通过ADF检验，序列非平稳（符合预期）
✅ **模型拟合**: 成功拟合ARIMA(1,1,1)模型
✅ **预测功能**: 成功预测未来3个月数据

### SARIMAX模型测试
✅ **SARIMAX导入**: SARIMAX模块成功导入
✅ **数据处理**: 成功处理Excel格式的月度数据
✅ **季节性建模**: 成功拟合SARIMAX(1,1,1)×(1,1,1,12)模型
✅ **参数优化**: 自动选择最优参数SARIMAX(5,2,4)×(1,1,1,12)
✅ **预测输出**: 成功预测未来6个月，AIC=909.91

### 完整分析测试
✅ **模型诊断**: 通过Ljung-Box检验，残差分析正常
✅ **可视化输出**: 生成完整的分析图表和预测图
✅ **文件保存**: 所有结果文件正确保存到results目录
✅ **模型比较**: SARIMAX在季节性数据上表现优于ARIMA

### 示例数据测试
✅ **合成数据**: 使用36个月示例数据测试两种模型
✅ **模型适应性**: 两种模型都能适应不同数据特征
✅ **预测稳定性**: 预测结果合理且稳定

## 预测结果示例

### ARIMA模型：代理购电6个月预测
| 月份 | 预测值(万千瓦时) | 下限 | 上限 |
|------|------------------|------|------|
| 2025-07 | 3821.68 | 3304.70 | 4338.67 |
| 2025-08 | 3723.18 | 3075.87 | 4370.48 |
| 2025-09 | 3582.29 | 2818.76 | 4345.82 |
| 2025-10 | 3146.76 | 2283.36 | 4010.15 |
| 2025-11 | 3172.60 | 2219.66 | 4125.55 |
| 2025-12 | 3859.62 | 2824.87 | 4894.37 |

### SARIMAX模型：代理购电6个月预测
| 月份 | 预测值(万千瓦时) | 下限 | 上限 |
|------|------------------|------|------|
| 2025-07 | 3843.44 | 3490.59 | 4196.30 |
| 2025-08 | 3766.78 | 3181.74 | 4351.82 |
| 2025-09 | 3833.30 | 3057.71 | 4608.89 |
| 2025-10 | 3459.09 | 2499.70 | 4418.48 |
| 2025-11 | 3506.56 | 2370.79 | 4642.34 |
| 2025-12 | 4332.89 | 3038.69 | 5627.09 |

## 模型性能

### ARIMA模型参数
- **最优ARIMA参数**: (0, 2, 1)
- **AIC值**: 1703.60
- **观测数**: 42个月
- **数据范围**: 2022年1月 - 2025年6月

### SARIMAX模型参数
- **最优SARIMAX参数**: (5, 2, 4)×(1, 1, 1, 12)
- **AIC值**: 909.91（优于ARIMA）
- **观测数**: 42个月
- **数据范围**: 2022年1月 - 2025年6月

### 模型诊断
- **Ljung-Box检验**: p值 > 0.05，残差无自相关
- **平稳性**: 通过ADF检验
- **收敛性**: 模型成功收敛

## 使用方法

### 1. 快速开始（ARIMA模型）
```bash
cd ARIMA
python run_arima_prediction.py --model ARIMA
```

### 2. 使用SARIMAX模型
```bash
python run_arima_prediction.py --model SARIMAX --months 6
```

### 3. 自定义预测
```bash
python run_arima_prediction.py --target 居民 --months 12 --model SARIMAX
```

### 4. 交互式使用
```bash
python arima_monthly_predictor.py
```

### 5. 编程接口
```python
from arima_monthly_predictor import ARIMAMonthlyPredictor

# ARIMA模型
arima_predictor = ARIMAMonthlyPredictor(
    target_column='代理购电',
    model_type='ARIMA'
)
arima_predictions = arima_predictor.run_complete_analysis(prediction_months=12)

# SARIMAX模型
sarimax_predictor = ARIMAMonthlyPredictor(
    target_column='代理购电',
    model_type='SARIMAX'
)
sarimax_predictions = sarimax_predictor.run_complete_analysis(prediction_months=12)
```

## 技术特点

### 优势
1. **双模型支持**: 同时支持ARIMA和SARIMAX模型，适应不同数据特征
2. **自动化程度高**: 自动参数选择和模型优化
3. **功能完整**: 从数据处理到结果输出的完整流程
4. **可视化丰富**: 多种图表帮助理解分析结果
5. **易于使用**: 提供多种使用方式和详细文档
6. **扩展性强**: 支持多种目标变量和自定义参数
7. **格式兼容**: 支持CSV和Excel多种数据格式

### 适用场景
- 电力需求月度预测（季节性和非季节性）
- 能源消费趋势分析
- 电网规划支持
- 政策影响评估
- 季节性业务预测

## 依赖环境

- Python 3.8+
- pandas >= 1.5.0
- numpy >= 1.23.0
- matplotlib >= 3.7.0
- statsmodels >= 0.14.0
- scikit-learn >= 1.2.0

## 注意事项

1. **数据质量**: 确保输入数据完整，建议至少24个月历史数据
2. **季节性**: 对于强季节性数据，可考虑使用SARIMA模型
3. **异常值**: 建议预处理时处理明显异常值
4. **预测区间**: 长期预测的不确定性会增加

## 未来改进方向

1. **模型集成**: 实现ARIMA和SARIMAX模型的自动集成，提高预测准确性
2. **外生变量**: 扩展SARIMAX模型，纳入天气、温度等外部因素
3. **深度学习**: 集成LSTM、GRU等深度学习模型用于复杂模式识别
4. **自动模型选择**: 基于数据特征自动选择最优模型类型
5. **实时更新**: 支持数据实时更新和模型重训练
6. **Web界面**: 开发Web界面便于非技术用户使用
7. **预测评估**: 增加更多评估指标和模型比较功能

## 总结

本时间序列月度预测项目成功实现了对电力数据的双模型时间序列分析和预测功能。通过完整的测试验证，ARIMA和SARIMAX两种模型都能够稳定运行并产生合理的预测结果。项目提供了丰富的功能和友好的使用接口，可以有效支持电力行业的月度预测需求。

### 主要成就
1. **成功实现双模型支持**: ARIMA适用于非季节性数据，SARIMAX适用于季节性数据
2. **完整的自动化流程**: 从数据加载到预测输出的全自动化处理
3. **优秀的预测性能**: SARIMAX模型在季节性数据上表现优异（AIC=909.91）
4. **丰富的可视化输出**: 提供完整的分析图表和诊断信息
5. **用户友好的接口**: 支持命令行、交互式和编程多种使用方式

项目代码结构清晰，文档完善，具有良好的可维护性和扩展性，为后续的功能增强和应用推广奠定了坚实基础。特别是SARIMAX模型的成功集成，使得系统能够更好地处理具有季节性特征的电力数据，显著提升了预测准确性。
