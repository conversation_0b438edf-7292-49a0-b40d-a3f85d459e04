from torch.utils.data import Dataset
import torch


class TimeSeriesDataset(Dataset):
    def __init__(self, data, timestamps, seq_len, label_len, pred_len, step=1):
        self.data = data
        self.timestamps = timestamps
        self.seq_len = seq_len
        self.label_len = label_len
        self.pred_len = pred_len
        self.step = step
        self.total_len = seq_len + pred_len
        
    def __len__(self):
        return (len(self.data) - self.total_len) // self.step + 1

    def __getitem__(self, idx):
        start = idx * self.step
        end = start + self.seq_len
        pred_end = end + self.pred_len
        
        # 序列数据
        batch_x = self.data[start:end]
        batch_y = self.data[end:pred_end]
        
        # 时间戳特征（简化处理，使用索引作为时间戳）
        batch_x_mark = torch.arange(start, end).float()
        batch_y_mark = torch.arange(end, pred_end).float()
        
        return batch_x, batch_y, batch_x_mark, batch_y_mark