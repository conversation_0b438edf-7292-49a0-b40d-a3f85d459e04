#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Daily-Level Weather-Electricity Correlation Analysis
====================================================

This script performs comprehensive correlation analysis between daily weather data 
and daily electricity consumption for three categories: 居民 (residential), 
农业 (agricultural), and 代理购电 (proxy electricity purchase).

Features:
- Pearson and Spearman correlation coefficients
- Statistical significance testing (p-values)
- Correlation heatmaps
- Structured CSV output with correlation results
- Filtering for statistically significant high correlations
- Timestamped output files

Author: Augment Agent
Date: 2025-07-16
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import pearsonr, spearmanr
import os
from datetime import datetime
import warnings

warnings.filterwarnings('ignore')

# Set Chinese font for matplotlib
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class DailyWeatherElectricityCorrelation:
    """Daily-level weather-electricity correlation analysis"""
    
    def __init__(self, data_path='data/整合天气数据_代理购电_20250716_094327.xlsx'):
        """
        Initialize the correlation analyzer
        
        Args:
            data_path: Path to the integrated daily weather-electricity data file
        """
        self.data_path = data_path
        self.df = None
        self.weather_columns = []
        self.electricity_columns = ['居民', '农业', '代理购电']
        self.correlation_results = {}
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
    def load_data(self):
        """Load and prepare the daily data"""
        print(f"Loading daily data from: {self.data_path}")
        
        # Load the integrated data
        self.df = pd.read_excel(self.data_path)
        
        # Convert date column
        self.df['日期'] = pd.to_datetime(self.df['日期'])
        
        # Identify weather columns (exclude date and electricity columns)
        exclude_cols = ['日期'] + self.electricity_columns
        self.weather_columns = [col for col in self.df.columns if col not in exclude_cols]
        
        print(f"Data loaded successfully:")
        print(f"  - Total records: {len(self.df)}")
        print(f"  - Date range: {self.df['日期'].min()} to {self.df['日期'].max()}")
        print(f"  - Weather variables: {len(self.weather_columns)}")
        print(f"  - Electricity categories: {len(self.electricity_columns)}")
        
        # Display basic statistics
        print(f"\nWeather variables: {self.weather_columns}")
        print(f"Electricity categories: {self.electricity_columns}")
        
        return self.df
    
    def calculate_correlations(self):
        """Calculate Pearson and Spearman correlations with p-values"""
        print("\nCalculating correlations...")
        
        results = []
        
        for elec_col in self.electricity_columns:
            for weather_col in self.weather_columns:
                # Get clean data (remove NaN values)
                elec_data = self.df[elec_col].dropna()
                weather_data = self.df[weather_col].dropna()
                
                # Find common indices
                common_idx = elec_data.index.intersection(weather_data.index)
                if len(common_idx) < 10:  # Need at least 10 data points
                    continue
                
                elec_clean = elec_data.loc[common_idx]
                weather_clean = weather_data.loc[common_idx]
                
                # Calculate Pearson correlation
                try:
                    pearson_corr, pearson_p = pearsonr(weather_clean, elec_clean)
                except:
                    pearson_corr, pearson_p = np.nan, np.nan
                
                # Calculate Spearman correlation
                try:
                    spearman_corr, spearman_p = spearmanr(weather_clean, elec_clean)
                except:
                    spearman_corr, spearman_p = np.nan, np.nan
                
                # Store results
                result = {
                    'electricity_type': elec_col,
                    'weather_variable': weather_col,
                    'pearson_correlation': pearson_corr,
                    'pearson_p_value': pearson_p,
                    'spearman_correlation': spearman_corr,
                    'spearman_p_value': spearman_p,
                    'sample_size': len(common_idx),
                    'pearson_significant': pearson_p < 0.05 if not np.isnan(pearson_p) else False,
                    'spearman_significant': spearman_p < 0.05 if not np.isnan(spearman_p) else False,
                    'pearson_abs_corr': abs(pearson_corr) if not np.isnan(pearson_corr) else 0,
                    'spearman_abs_corr': abs(spearman_corr) if not np.isnan(spearman_corr) else 0
                }
                
                results.append(result)
        
        self.correlation_results = pd.DataFrame(results)
        print(f"Calculated correlations for {len(results)} weather-electricity pairs")
        
        return self.correlation_results
    
    def filter_significant_correlations(self, min_correlation=0.3, significance_level=0.05):
        """Filter for statistically significant high correlations"""
        print(f"\nFiltering for significant correlations (|r| >= {min_correlation}, p < {significance_level})...")
        
        # Filter for Pearson correlations
        pearson_significant = self.correlation_results[
            (self.correlation_results['pearson_abs_corr'] >= min_correlation) &
            (self.correlation_results['pearson_p_value'] < significance_level)
        ].copy()
        
        # Filter for Spearman correlations
        spearman_significant = self.correlation_results[
            (self.correlation_results['spearman_abs_corr'] >= min_correlation) &
            (self.correlation_results['spearman_p_value'] < significance_level)
        ].copy()
        
        print(f"Found {len(pearson_significant)} significant Pearson correlations")
        print(f"Found {len(spearman_significant)} significant Spearman correlations")
        
        return pearson_significant, spearman_significant

    def create_correlation_heatmaps(self):
        """Create correlation heatmaps for visualization"""
        print("\nCreating correlation heatmaps...")

        # Create correlation matrices for each electricity type
        for elec_type in self.electricity_columns:
            # Filter data for this electricity type
            elec_data = self.correlation_results[
                self.correlation_results['electricity_type'] == elec_type
            ].copy()

            if len(elec_data) == 0:
                continue

            # Create Pearson correlation matrix
            pearson_matrix = elec_data.pivot_table(
                index='weather_variable',
                columns='electricity_type',
                values='pearson_correlation'
            )

            # Create Spearman correlation matrix
            spearman_matrix = elec_data.pivot_table(
                index='weather_variable',
                columns='electricity_type',
                values='spearman_correlation'
            )

            # Plot heatmaps
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 12))

            # Pearson heatmap
            sns.heatmap(pearson_matrix, annot=True, cmap='RdBu_r', center=0,
                       fmt='.3f', ax=ax1, cbar_kws={'label': 'Pearson Correlation'})
            ax1.set_title(f'Daily Pearson Correlations: {elec_type} vs Weather Variables')
            ax1.set_xlabel('Electricity Type')
            ax1.set_ylabel('Weather Variables')

            # Spearman heatmap
            sns.heatmap(spearman_matrix, annot=True, cmap='RdBu_r', center=0,
                       fmt='.3f', ax=ax2, cbar_kws={'label': 'Spearman Correlation'})
            ax2.set_title(f'Daily Spearman Correlations: {elec_type} vs Weather Variables')
            ax2.set_xlabel('Electricity Type')
            ax2.set_ylabel('Weather Variables')

            plt.tight_layout()

            # Save heatmap
            heatmap_filename = f'feature_analysis_results/daily_correlation_heatmap_{elec_type}_{self.timestamp}.png'
            plt.savefig(heatmap_filename, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"Saved heatmap: {heatmap_filename}")

        # Create overall correlation heatmap
        self.create_overall_heatmap()

    def create_overall_heatmap(self):
        """Create overall correlation heatmap for all electricity types"""
        print("Creating overall correlation heatmap...")

        # Prepare data for overall heatmap
        pearson_data = []
        spearman_data = []

        for _, row in self.correlation_results.iterrows():
            pearson_data.append({
                'Weather_Variable': row['weather_variable'],
                row['electricity_type']: row['pearson_correlation']
            })
            spearman_data.append({
                'Weather_Variable': row['weather_variable'],
                row['electricity_type']: row['spearman_correlation']
            })

        # Convert to DataFrames
        pearson_df = pd.DataFrame(pearson_data).groupby('Weather_Variable').first()
        spearman_df = pd.DataFrame(spearman_data).groupby('Weather_Variable').first()

        # Create combined heatmap
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 16))

        # Pearson heatmap
        sns.heatmap(pearson_df, annot=True, cmap='RdBu_r', center=0,
                   fmt='.3f', ax=ax1, cbar_kws={'label': 'Pearson Correlation'})
        ax1.set_title('Daily Pearson Correlations: Weather Variables vs Electricity Types')
        ax1.set_xlabel('Electricity Types')
        ax1.set_ylabel('Weather Variables')

        # Spearman heatmap
        sns.heatmap(spearman_df, annot=True, cmap='RdBu_r', center=0,
                   fmt='.3f', ax=ax2, cbar_kws={'label': 'Spearman Correlation'})
        ax2.set_title('Daily Spearman Correlations: Weather Variables vs Electricity Types')
        ax2.set_xlabel('Electricity Types')
        ax2.set_ylabel('Weather Variables')

        plt.tight_layout()

        # Save overall heatmap
        overall_heatmap_filename = f'feature_analysis_results/daily_overall_correlation_heatmap_{self.timestamp}.png'
        plt.savefig(overall_heatmap_filename, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"Saved overall heatmap: {overall_heatmap_filename}")

    def save_results(self):
        """Save correlation results to CSV files"""
        print("\nSaving correlation results...")

        # Save complete results
        complete_filename = f'feature_analysis_results/daily_weather_electricity_correlations_{self.timestamp}.csv'
        self.correlation_results.to_csv(complete_filename, index=False, encoding='utf-8-sig')
        print(f"Saved complete results: {complete_filename}")

        # Save significant correlations
        pearson_sig, spearman_sig = self.filter_significant_correlations()

        if len(pearson_sig) > 0:
            pearson_filename = f'feature_analysis_results/daily_significant_pearson_correlations_{self.timestamp}.csv'
            pearson_sig.to_csv(pearson_filename, index=False, encoding='utf-8-sig')
            print(f"Saved significant Pearson correlations: {pearson_filename}")

        if len(spearman_sig) > 0:
            spearman_filename = f'feature_analysis_results/daily_significant_spearman_correlations_{self.timestamp}.csv'
            spearman_sig.to_csv(spearman_filename, index=False, encoding='utf-8-sig')
            print(f"Saved significant Spearman correlations: {spearman_filename}")

        return complete_filename, pearson_filename if len(pearson_sig) > 0 else None, spearman_filename if len(spearman_sig) > 0 else None

    def generate_summary_report(self):
        """Generate a summary report of the correlation analysis"""
        print("\nGenerating summary report...")

        # Calculate summary statistics
        summary_stats = {}

        for elec_type in self.electricity_columns:
            elec_data = self.correlation_results[
                self.correlation_results['electricity_type'] == elec_type
            ]

            if len(elec_data) == 0:
                continue

            # Pearson statistics
            pearson_corrs = elec_data['pearson_correlation'].dropna()
            pearson_sig_count = elec_data['pearson_significant'].sum()

            # Spearman statistics
            spearman_corrs = elec_data['spearman_correlation'].dropna()
            spearman_sig_count = elec_data['spearman_significant'].sum()

            summary_stats[elec_type] = {
                'total_weather_variables': len(elec_data),
                'pearson_mean_abs_corr': pearson_corrs.abs().mean(),
                'pearson_max_abs_corr': pearson_corrs.abs().max(),
                'pearson_significant_count': pearson_sig_count,
                'spearman_mean_abs_corr': spearman_corrs.abs().mean(),
                'spearman_max_abs_corr': spearman_corrs.abs().max(),
                'spearman_significant_count': spearman_sig_count
            }

        # Save summary report
        summary_df = pd.DataFrame(summary_stats).T
        summary_filename = f'feature_analysis_results/daily_correlation_summary_{self.timestamp}.csv'
        summary_df.to_csv(summary_filename, encoding='utf-8-sig')

        print(f"Saved summary report: {summary_filename}")

        # Print summary to console
        print("\n" + "="*80)
        print("DAILY WEATHER-ELECTRICITY CORRELATION ANALYSIS SUMMARY")
        print("="*80)

        for elec_type in self.electricity_columns:
            if elec_type in summary_stats:
                stats = summary_stats[elec_type]
                print(f"\n{elec_type} (Electricity Type):")
                print(f"  Total weather variables analyzed: {stats['total_weather_variables']}")
                print(f"  Pearson correlations:")
                print(f"    Mean |correlation|: {stats['pearson_mean_abs_corr']:.3f}")
                print(f"    Max |correlation|: {stats['pearson_max_abs_corr']:.3f}")
                print(f"    Significant correlations (p<0.05): {stats['pearson_significant_count']}")
                print(f"  Spearman correlations:")
                print(f"    Mean |correlation|: {stats['spearman_mean_abs_corr']:.3f}")
                print(f"    Max |correlation|: {stats['spearman_max_abs_corr']:.3f}")
                print(f"    Significant correlations (p<0.05): {stats['spearman_significant_count']}")

        return summary_filename

    def run_complete_analysis(self):
        """Run the complete daily correlation analysis"""
        print("="*80)
        print("DAILY WEATHER-ELECTRICITY CORRELATION ANALYSIS")
        print("="*80)

        # Load data
        self.load_data()

        # Calculate correlations
        self.calculate_correlations()

        # Create visualizations
        self.create_correlation_heatmaps()

        # Save results
        complete_file, pearson_file, spearman_file = self.save_results()

        # Generate summary
        summary_file = self.generate_summary_report()

        print("\n" + "="*80)
        print("ANALYSIS COMPLETE!")
        print("="*80)
        print("Generated files:")
        print(f"  - Complete results: {complete_file}")
        if pearson_file:
            print(f"  - Significant Pearson correlations: {pearson_file}")
        if spearman_file:
            print(f"  - Significant Spearman correlations: {spearman_file}")
        print(f"  - Summary report: {summary_file}")
        print(f"  - Correlation heatmaps: feature_analysis_results/daily_*_heatmap_{self.timestamp}.png")

        return {
            'complete_results': complete_file,
            'significant_pearson': pearson_file,
            'significant_spearman': spearman_file,
            'summary': summary_file,
            'timestamp': self.timestamp
        }


def main():
    """Main execution function"""
    try:
        # Initialize analyzer
        analyzer = DailyWeatherElectricityCorrelation()

        # Run complete analysis
        results = analyzer.run_complete_analysis()

        print(f"\nDaily correlation analysis completed successfully!")
        print(f"Results saved with timestamp: {results['timestamp']}")

    except Exception as e:
        print(f"Error during analysis: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
