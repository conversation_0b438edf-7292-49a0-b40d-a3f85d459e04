#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Automatic Feature Selection Module
==================================

This module provides automatic weather feature selection for electricity prediction models
based on correlation analysis results. It supports MS (multivariate input, single output) mode
by automatically selecting weather features that have significant correlations with the target
electricity type.

Features:
- Automatic detection and loading of the most recent correlation analysis results
- Feature filtering based on statistical significance
- Fallback mechanisms for robustness
- Consistent feature selection between training and prediction
- Comprehensive logging and validation

Author: Augment Agent
Date: 2025-07-16
"""

import os
import pandas as pd
import glob
import re
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import warnings

warnings.filterwarnings('ignore')


class AutoFeatureSelector:
    """Automatic feature selector based on correlation analysis results"""
    
    def __init__(self, correlation_results_dir='feature_analysis_results'):
        """
        Initialize the feature selector
        
        Args:
            correlation_results_dir: Directory containing correlation analysis results
        """
        self.correlation_results_dir = correlation_results_dir
        self.correlation_file_pattern = 'daily_significant_spearman_correlations_*.csv'
        self.selected_features_cache = {}
        
    def find_latest_correlation_file(self) -> Optional[str]:
        """
        Find the most recent Spearman correlation results file
        
        Returns:
            Path to the most recent correlation file, or None if not found
        """
        try:
            # Search for correlation files
            search_pattern = os.path.join(self.correlation_results_dir, self.correlation_file_pattern)
            correlation_files = glob.glob(search_pattern)
            
            if not correlation_files:
                print(f"Warning: No correlation files found in {self.correlation_results_dir}")
                print(f"Expected pattern: {self.correlation_file_pattern}")
                return None
            
            # Extract timestamps and find the latest
            file_timestamps = []
            for file_path in correlation_files:
                filename = os.path.basename(file_path)
                # Extract timestamp from filename: daily_significant_spearman_correlations_YYYYMMDD_HHMMSS.csv
                timestamp_match = re.search(r'(\d{8}_\d{6})\.csv$', filename)
                if timestamp_match:
                    timestamp_str = timestamp_match.group(1)
                    try:
                        timestamp = datetime.strptime(timestamp_str, '%Y%m%d_%H%M%S')
                        file_timestamps.append((timestamp, file_path))
                    except ValueError:
                        print(f"Warning: Could not parse timestamp from {filename}")
                        continue
            
            if not file_timestamps:
                print("Warning: No valid timestamp found in correlation files")
                return None
            
            # Sort by timestamp and get the latest
            file_timestamps.sort(key=lambda x: x[0], reverse=True)
            latest_file = file_timestamps[0][1]
            latest_timestamp = file_timestamps[0][0]
            
            print(f"Found latest correlation file: {latest_file}")
            print(f"Timestamp: {latest_timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
            
            return latest_file
            
        except Exception as e:
            print(f"Error finding correlation file: {str(e)}")
            return None
    
    def load_correlation_results(self, file_path: str) -> Optional[pd.DataFrame]:
        """
        Load correlation results from CSV file
        
        Args:
            file_path: Path to the correlation results file
            
        Returns:
            DataFrame with correlation results, or None if loading fails
        """
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            
            # Validate required columns
            required_columns = [
                'electricity_type', 'weather_variable', 'spearman_correlation',
                'spearman_p_value', 'spearman_significant'
            ]
            
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                print(f"Error: Missing required columns in correlation file: {missing_columns}")
                return None
            
            print(f"Loaded correlation results: {len(df)} records")
            return df
            
        except Exception as e:
            print(f"Error loading correlation file {file_path}: {str(e)}")
            return None
    
    def filter_significant_features(self, correlation_df: pd.DataFrame, 
                                   target_electricity_type: str,
                                   min_correlation: float = 0.3,
                                   significance_level: float = 0.05) -> List[str]:
        """
        Filter weather features that have significant correlations with the target electricity type
        
        Args:
            correlation_df: DataFrame with correlation results
            target_electricity_type: Target electricity type (居民/农业/代理购电)
            min_correlation: Minimum absolute correlation threshold
            significance_level: Maximum p-value for significance
            
        Returns:
            List of significant weather feature names
        """
        try:
            # Filter for the target electricity type
            target_data = correlation_df[
                correlation_df['electricity_type'] == target_electricity_type
            ].copy()
            
            if len(target_data) == 0:
                print(f"Warning: No correlation data found for electricity type: {target_electricity_type}")
                return []
            
            # Filter for significant correlations
            significant_features = target_data[
                (target_data['spearman_significant'] == True) &
                (abs(target_data['spearman_correlation']) >= min_correlation) &
                (target_data['spearman_p_value'] <= significance_level)
            ].copy()
            
            if len(significant_features) == 0:
                print(f"Warning: No significant features found for {target_electricity_type}")
                print(f"Criteria: |correlation| >= {min_correlation}, p <= {significance_level}")
                return []
            
            # Sort by absolute correlation (descending)
            significant_features['abs_correlation'] = abs(significant_features['spearman_correlation'])
            significant_features = significant_features.sort_values('abs_correlation', ascending=False)
            
            feature_list = significant_features['weather_variable'].tolist()
            
            print(f"Selected {len(feature_list)} significant weather features for {target_electricity_type}:")
            for i, (_, row) in enumerate(significant_features.iterrows()):
                print(f"  {i+1}. {row['weather_variable']}: r={row['spearman_correlation']:.3f}, p={row['spearman_p_value']:.4f}")
            
            return feature_list

        except Exception as e:
            print(f"Error filtering significant features: {str(e)}")
            return []

    def extract_correlation_coefficients(self, correlation_df: pd.DataFrame,
                                       target_electricity_type: str,
                                       selected_features: List[str]) -> Dict[str, float]:
        """
        Extract correlation coefficients for selected weather features

        Args:
            correlation_df: DataFrame with correlation results
            target_electricity_type: Target electricity type (居民/农业/代理购电)
            selected_features: List of selected weather feature names

        Returns:
            Dictionary mapping feature names to their Spearman correlation coefficients
        """
        try:
            # Filter for the target electricity type
            target_data = correlation_df[
                correlation_df['electricity_type'] == target_electricity_type
            ].copy()

            if len(target_data) == 0:
                print(f"Warning: No correlation data found for electricity type: {target_electricity_type}")
                return {}

            # Extract correlation coefficients for selected features
            correlation_coefficients = {}
            for feature in selected_features:
                # Skip target variable (it's not a weather feature)
                if feature == target_electricity_type:
                    continue

                feature_data = target_data[target_data['weather_variable'] == feature]
                if len(feature_data) > 0:
                    correlation_coefficients[feature] = feature_data.iloc[0]['spearman_correlation']
                else:
                    print(f"Warning: No correlation data found for feature: {feature}")

            return correlation_coefficients

        except Exception as e:
            print(f"Error extracting correlation coefficients: {str(e)}")
            return {}

    def calculate_correlation_weights(self, correlation_coefficients: Dict[str, float]) -> Dict[str, float]:
        """
        Calculate normalized weights based on absolute correlation coefficients

        Args:
            correlation_coefficients: Dict mapping feature names to correlation coefficients

        Returns:
            Dictionary mapping feature names to normalized weights (sum to 1.0)
        """
        try:
            if not correlation_coefficients:
                return {}

            # Calculate absolute correlation values
            abs_correlations = {feature: abs(corr) for feature, corr in correlation_coefficients.items()}

            # Calculate sum of absolute correlations
            total_abs_correlation = sum(abs_correlations.values())

            if total_abs_correlation == 0:
                # Equal weights if all correlations are zero
                num_features = len(correlation_coefficients)
                return {feature: 1.0 / num_features for feature in correlation_coefficients.keys()}

            # Normalize weights so they sum to 1.0
            weights = {
                feature: abs_corr / total_abs_correlation
                for feature, abs_corr in abs_correlations.items()
            }

            print(f"Calculated correlation-based weights:")
            for feature, weight in weights.items():
                corr = correlation_coefficients[feature]
                print(f"  {feature}: weight={weight:.3f} (r={corr:.3f})")

            return weights

        except Exception as e:
            print(f"Error calculating correlation weights: {str(e)}")
            return {}
    
    def get_selected_features(self, target_electricity_type: str,
                            data_columns: List[str],
                            correlation_file_path: Optional[str] = None,
                            min_correlation: float = 0.3,
                            significance_level: float = 0.05) -> Tuple[List[str], str]:
        """
        Get selected features for the target electricity type
        
        Args:
            target_electricity_type: Target electricity type (居民/农业/代理购电)
            data_columns: All available data columns
            correlation_file_path: Optional path to specific correlation file
            min_correlation: Minimum absolute correlation threshold
            significance_level: Maximum p-value for significance
            
        Returns:
            Tuple of (selected_feature_list, selection_method)
            selection_method: 'correlation_based', 'fallback_all', or 'fallback_single'
        """
        try:
            # Use provided file or find the latest one
            if correlation_file_path is None:
                correlation_file_path = self.find_latest_correlation_file()
            
            if correlation_file_path is None:
                print("Warning: No correlation file found, falling back to all weather features")
                weather_features = [col for col in data_columns if col != target_electricity_type]
                return weather_features, 'fallback_all'
            
            # Load correlation results
            correlation_df = self.load_correlation_results(correlation_file_path)
            if correlation_df is None:
                print("Warning: Failed to load correlation results, falling back to all weather features")
                weather_features = [col for col in data_columns if col != target_electricity_type]
                return weather_features, 'fallback_all'
            
            # Filter significant features
            significant_weather_features = self.filter_significant_features(
                correlation_df, target_electricity_type, min_correlation, significance_level
            )
            
            if not significant_weather_features:
                print("Warning: No significant weather features found, falling back to single variable mode")
                return [target_electricity_type], 'fallback_single'
            
            # Validate that selected features exist in data
            available_weather_features = []
            for feature in significant_weather_features:
                if feature in data_columns:
                    available_weather_features.append(feature)
                else:
                    print(f"Warning: Selected feature '{feature}' not found in data columns")
            
            if not available_weather_features:
                print("Warning: No selected features available in data, falling back to all weather features")
                weather_features = [col for col in data_columns if col != target_electricity_type]
                return weather_features, 'fallback_all'
            
            # Include target column and selected weather features
            selected_features = [target_electricity_type] + available_weather_features
            
            print(f"Final feature selection for {target_electricity_type}:")
            print(f"  Target: {target_electricity_type}")
            print(f"  Weather features: {available_weather_features}")
            print(f"  Total features: {len(selected_features)}")
            
            return selected_features, 'correlation_based'
            
        except Exception as e:
            print(f"Error in feature selection: {str(e)}")
            print("Falling back to all available features")
            return data_columns, 'fallback_all'

    def validate_feature_consistency(self, training_features: List[str],
                                   prediction_features: List[str]) -> bool:
        """
        Validate that training and prediction use the same features

        Args:
            training_features: Features used during training
            prediction_features: Features to be used for prediction

        Returns:
            True if features are consistent, False otherwise
        """
        if set(training_features) != set(prediction_features):
            print("Error: Feature inconsistency detected!")
            print(f"Training features: {training_features}")
            print(f"Prediction features: {prediction_features}")

            missing_in_prediction = set(training_features) - set(prediction_features)
            extra_in_prediction = set(prediction_features) - set(training_features)

            if missing_in_prediction:
                print(f"Missing in prediction: {list(missing_in_prediction)}")
            if extra_in_prediction:
                print(f"Extra in prediction: {list(extra_in_prediction)}")

            return False

        return True

    def save_feature_selection_log(self, target_electricity_type: str,
                                 selected_features: List[str],
                                 selection_method: str,
                                 correlation_file_path: Optional[str] = None,
                                 output_dir: str = 'feature_analysis_results') -> str:
        """
        Save feature selection log for reproducibility

        Args:
            target_electricity_type: Target electricity type
            selected_features: List of selected features
            selection_method: Method used for selection
            correlation_file_path: Path to correlation file used
            output_dir: Directory to save the log

        Returns:
            Path to the saved log file
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            log_filename = f"{output_dir}/feature_selection_log_{target_electricity_type}_{timestamp}.txt"

            os.makedirs(output_dir, exist_ok=True)

            with open(log_filename, 'w', encoding='utf-8') as f:
                f.write("Automatic Feature Selection Log\n")
                f.write("=" * 50 + "\n")
                f.write(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Target Electricity Type: {target_electricity_type}\n")
                f.write(f"Selection Method: {selection_method}\n")
                f.write(f"Correlation File: {correlation_file_path or 'None'}\n")
                f.write(f"Total Features Selected: {len(selected_features)}\n\n")

                f.write("Selected Features:\n")
                for i, feature in enumerate(selected_features):
                    f.write(f"  {i+1}. {feature}\n")

                f.write(f"\nSelection Method Details:\n")
                if selection_method == 'correlation_based':
                    f.write("  - Features selected based on significant Spearman correlations\n")
                    f.write("  - Minimum correlation threshold: 0.3\n")
                    f.write("  - Significance level: 0.05\n")
                elif selection_method == 'fallback_all':
                    f.write("  - Fallback to all available weather features\n")
                    f.write("  - Reason: No correlation file found or failed to load\n")
                elif selection_method == 'fallback_single':
                    f.write("  - Fallback to single variable mode\n")
                    f.write("  - Reason: No significant correlations found\n")

            print(f"Feature selection log saved: {log_filename}")
            return log_filename

        except Exception as e:
            print(f"Error saving feature selection log: {str(e)}")
            return ""

    def get_feature_selection_for_ms_mode(self, target_electricity_type: str,
                                        all_data_columns: List[str],
                                        correlation_file_path: Optional[str] = None) -> Dict[str, any]:
        """
        Main interface for MS mode feature selection

        Args:
            target_electricity_type: Target electricity type (居民/农业/代理购电)
            all_data_columns: All available data columns
            correlation_file_path: Optional path to specific correlation file

        Returns:
            Dictionary with feature selection results
        """
        print(f"\n{'='*60}")
        print(f"AUTOMATIC FEATURE SELECTION FOR MS MODE")
        print(f"Target: {target_electricity_type}")
        print(f"{'='*60}")

        # Get selected features
        selected_features, selection_method = self.get_selected_features(
            target_electricity_type=target_electricity_type,
            data_columns=all_data_columns,
            correlation_file_path=correlation_file_path
        )

        # Initialize correlation coefficients and weights
        correlation_coefficients = {}
        feature_weights = {}

        # Extract correlation coefficients and calculate weights for correlation-based selection
        if selection_method == 'correlation_based':
            # Use provided file or find the latest one
            if correlation_file_path is None:
                correlation_file_path = self.find_latest_correlation_file()

            if correlation_file_path:
                correlation_df = self.load_correlation_results(correlation_file_path)
                if correlation_df is not None:
                    # Extract correlation coefficients for selected features
                    correlation_coefficients = self.extract_correlation_coefficients(
                        correlation_df, target_electricity_type, selected_features
                    )

                    # Calculate correlation-based weights
                    feature_weights = self.calculate_correlation_weights(correlation_coefficients)

        # Determine final mode based on selection results
        if selection_method == 'fallback_single':
            final_mode = 'S'  # Fall back to single variable mode
            print(f"\nFalling back to single variable (S) mode")
        else:
            final_mode = 'MS'  # Keep MS mode
            print(f"\nUsing multivariate single output (MS) mode")
            if feature_weights:
                print(f"Feature weighting enabled based on correlation coefficients")

        # Save selection log
        log_file = self.save_feature_selection_log(
            target_electricity_type=target_electricity_type,
            selected_features=selected_features,
            selection_method=selection_method,
            correlation_file_path=correlation_file_path
        )

        result = {
            'selected_features': selected_features,
            'selection_method': selection_method,
            'final_mode': final_mode,
            'correlation_file_path': correlation_file_path,
            'log_file': log_file,
            'target_electricity_type': target_electricity_type,
            'correlation_coefficients': correlation_coefficients,
            'feature_weights': feature_weights
        }

        print(f"\nFeature selection completed:")
        print(f"  Selected features: {len(selected_features)}")
        print(f"  Selection method: {selection_method}")
        print(f"  Final mode: {final_mode}")

        return result


# Convenience functions for easy integration
def auto_select_features_for_ms_mode(target_electricity_type: str,
                                   all_data_columns: List[str],
                                   correlation_results_dir: str = 'feature_analysis_results',
                                   correlation_file_path: Optional[str] = None) -> Dict[str, any]:
    """
    Convenience function for automatic feature selection in MS mode

    Args:
        target_electricity_type: Target electricity type (居民/农业/代理购电)
        all_data_columns: All available data columns
        correlation_results_dir: Directory containing correlation analysis results
        correlation_file_path: Optional path to specific correlation file

    Returns:
        Dictionary with feature selection results
    """
    selector = AutoFeatureSelector(correlation_results_dir)
    return selector.get_feature_selection_for_ms_mode(
        target_electricity_type=target_electricity_type,
        all_data_columns=all_data_columns,
        correlation_file_path=correlation_file_path
    )


def validate_feature_consistency_for_prediction(training_features: List[str],
                                              prediction_features: List[str]) -> bool:
    """
    Convenience function to validate feature consistency between training and prediction

    Args:
        training_features: Features used during training
        prediction_features: Features to be used for prediction

    Returns:
        True if features are consistent, False otherwise
    """
    selector = AutoFeatureSelector()
    return selector.validate_feature_consistency(training_features, prediction_features)
