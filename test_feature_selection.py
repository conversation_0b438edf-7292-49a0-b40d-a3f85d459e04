#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for automatic feature selection implementation
"""

import sys
import os
import pandas as pd

def test_feature_selector():
    """Test the feature selector module"""
    print("Testing Feature Selection Implementation")
    print("=" * 60)
    
    try:
        # Import the feature selector
        from feature_selector import AutoFeatureSelector, auto_select_features_for_ms_mode
        print("✓ Successfully imported feature_selector module")
        
        # Test 1: Check if correlation files exist
        print("\n1. Checking correlation analysis files...")
        selector = AutoFeatureSelector()
        latest_file = selector.find_latest_correlation_file()
        
        if latest_file:
            print(f"✓ Found correlation file: {latest_file}")
        else:
            print("✗ No correlation files found")
            return False
        
        # Test 2: Load correlation results
        print("\n2. Loading correlation results...")
        correlation_df = selector.load_correlation_results(latest_file)
        
        if correlation_df is not None:
            print(f"✓ Loaded correlation data: {len(correlation_df)} records")
            print(f"  Electricity types: {correlation_df['electricity_type'].unique().tolist()}")
            print(f"  Weather variables: {len(correlation_df['weather_variable'].unique())}")
        else:
            print("✗ Failed to load correlation results")
            return False
        
        # Test 3: Load sample data
        print("\n3. Loading sample electricity data...")
        data_path = 'data/龙泉代理购电.xlsx'
        if not os.path.exists(data_path):
            print(f"✗ Data file not found: {data_path}")
            return False
        
        df = pd.read_excel(data_path)
        all_data_columns = [col for col in df.columns if col != '日期']
        print(f"✓ Loaded data with columns: {all_data_columns}")
        
        # Test 4: Test feature selection for each electricity type
        print("\n4. Testing feature selection for each electricity type...")
        
        for target in ['居民', '农业', '代理购电']:
            print(f"\n--- Testing {target} ---")
            
            try:
                result = auto_select_features_for_ms_mode(
                    target_electricity_type=target,
                    all_data_columns=all_data_columns,
                    correlation_results_dir='feature_analysis_results'
                )
                
                print(f"✓ Feature selection completed for {target}")
                print(f"  Selected features: {len(result['selected_features'])}")
                print(f"  Selection method: {result['selection_method']}")
                print(f"  Final mode: {result['final_mode']}")
                print(f"  Features: {result['selected_features']}")
                
            except Exception as e:
                print(f"✗ Feature selection failed for {target}: {str(e)}")
                return False
        
        print("\n" + "=" * 60)
        print("✓ All feature selection tests passed!")
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {str(e)}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_dataset_integration():
    """Test the ProxyPowerDataset integration"""
    print("\n" + "=" * 60)
    print("Testing ProxyPowerDataset Integration")
    print("=" * 60)
    
    try:
        from data_provider.sg_dataset import ProxyPowerDataset
        print("✓ Successfully imported ProxyPowerDataset")
        
        # Test with MS mode and auto feature selection
        print("\n1. Testing MS mode with auto feature selection...")
        
        dataset = ProxyPowerDataset(
            data_path='data/龙泉代理购电.xlsx',
            seq_len=96,
            label_len=24,
            pred_len=7,
            target_col='代理购电',
            features='MS',
            scale=True,
            auto_feature_selection=True
        )
        
        print(f"✓ Dataset created successfully")
        print(f"  Data shape: {dataset.data.shape}")
        print(f"  Features: {dataset.features}")
        print(f"  Data columns: {dataset.data_columns}")
        
        if hasattr(dataset, 'feature_selection_result') and dataset.feature_selection_result:
            result = dataset.feature_selection_result
            print(f"  Feature selection method: {result['selection_method']}")
            print(f"  Final mode: {result['final_mode']}")
        
        # Test data loading
        print("\n2. Testing data loading...")
        sample = dataset[0]
        print(f"✓ Sample data loaded: {len(sample)} elements")
        print(f"  Input shape: {sample[0].shape}")
        print(f"  Output shape: {sample[1].shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ Dataset integration test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function"""
    print("Automatic Feature Selection Implementation Test")
    print("=" * 80)
    
    # Test 1: Feature selector module
    test1_passed = test_feature_selector()
    
    # Test 2: Dataset integration
    test2_passed = test_dataset_integration()
    
    # Summary
    print("\n" + "=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)
    print(f"Feature Selector Module: {'✓ PASSED' if test1_passed else '✗ FAILED'}")
    print(f"Dataset Integration: {'✓ PASSED' if test2_passed else '✗ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 ALL TESTS PASSED! Feature selection implementation is working correctly.")
        return True
    else:
        print("\n❌ SOME TESTS FAILED. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
