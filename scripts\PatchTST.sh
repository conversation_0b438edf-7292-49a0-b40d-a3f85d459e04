export CUDA_VISIBLE_DEVICES=0

model_name=PatchTST
dataset=/data/tushihao/SOG/
gpu=0
lookback=3d
lookback_v=288

python -u run.py \
  --task_name long_term_sg_forecast \
  --is_training 1 \
  --gpu $gpu \
  --batch_size 4096 \
  --root_path $dataset \
  --data_path "zb_load_dataset_${lookback}_96p.pkl" \
  --model_id "zb_load_${lookback}_96p" \
  --train_epochs 5 \
  --model $model_name \
  --data zb_load \
  --features S \
  --seq_len $lookback_v \
  --label_len 48 \
  --pred_len 96 \
  --e_layers 2 \
  --d_layers 1 \
  --factor 3 \
  --enc_in 1 \
  --step 32 \
  --dec_in 1 \
  --c_out 1 \
  --des 'Exp' \
  --itr 1

python -u run.py \
  --task_name long_term_sg_forecast \
  --is_training 1 \
  --gpu $gpu \
  --batch_size 512 \
  --root_path $dataset \
  --data_path "zb_load_dataset_${lookback}_1p.pkl" \
  --model_id "zb_load_${lookback}_1p" \
  --train_epochs 10 \
  --model $model_name \
  --data zb_load \
  --features S \
  --seq_len $lookback_v \
  --label_len 48 \
  --pred_len 1 \
  --e_layers 2 \
  --d_layers 1 \
  --factor 3 \
  --enc_in 1 \
  --dec_in 1 \
  --step 32 \
  --c_out 1 \
  --des 'Exp' \
  --itr 1

python -u run.py \
  --task_name long_term_sg_forecast \
  --is_training 1 \
  --gpu $gpu \
  --batch_size 512 \
  --root_path $dataset \
  --data_path "zb_load_dataset_${lookback}_7d.pkl" \
  --model_id "zb_load_${lookback}_7d" \
  --train_epochs 10 \
  --model $model_name \
  --data zb_load \
  --features S \
  --seq_len $lookback_v \
  --label_len 48 \
  --pred_len 672 \
  --e_layers 2 \
  --d_layers 1 \
  --factor 3 \
  --enc_in 1 \
  --step 32 \
  --dec_in 1 \
  --c_out 1 \
  --des 'Exp' \
  --itr 1

python -u run.py \
  --task_name long_term_sg_forecast \
  --is_training 1 \
  --gpu $gpu \
  --batch_size 512 \
  --root_path $dataset \
  --data_path "zb_load_dataset_${lookback}_30d.pkl" \
  --model_id "zb_load_${lookback}_30d" \
  --train_epochs 10 \
  --model $model_name \
  --data zb_load \
  --features S \
  --seq_len $lookback_v \
  --label_len 48 \
  --pred_len 30 \
  --e_layers 2 \
  --d_layers 1 \
  --factor 3 \
  --enc_in 1 \
  --step 32 \
  --load_m 1 \
  --dec_in 1 \
  --c_out 1 \
  --des 'Exp' \
  --itr 1