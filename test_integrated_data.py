#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for integrated weather data feature selection
"""

from feature_selector import auto_select_features_for_ms_mode
import pandas as pd
from config_manager import Config<PERSON>ana<PERSON>

def test_integrated_data():
    """Test feature selection with integrated weather data"""
    print('Testing with integrated weather data...')
    
    # Get the correct data file path for MS mode
    data_path = ConfigManager.get_data_file_path('MS', True)
    print(f'Using data file: {data_path}')
    
    # Load the integrated data
    df = pd.read_excel(data_path)
    all_data_columns = [col for col in df.columns if col != '日期']
    print(f'Available columns: {all_data_columns}')
    
    # Test feature selection for each electricity type
    for target in ['居民', '农业', '代理购电']:
        print(f'\n=== Testing feature selection for {target} ===')
        
        result = auto_select_features_for_ms_mode(
            target_electricity_type=target,
            all_data_columns=all_data_columns,
            correlation_results_dir='feature_analysis_results'
        )
        
        print(f'Selected features: {result["selected_features"]}')
        print(f'Selection method: {result["selection_method"]}')
        print(f'Final mode: {result["final_mode"]}')
        
        if result["selection_method"] == "correlation_based":
            print("✓ Successfully selected features based on correlation analysis!")
        else:
            print(f"⚠ Used fallback method: {result['selection_method']}")

if __name__ == "__main__":
    test_integrated_data()
