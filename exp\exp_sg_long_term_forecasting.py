import pickle
from numpy.random import f
from torch.utils.data import Subset
from exp.exp_basic import Exp_Basic
from utils.tools import EarlyStopping, adjust_learning_rate, visual
from utils.metrics import metric
import torch
import torch.nn as nn
from tqdm import tqdm
from torch import optim
from torch.utils.data import DataLoader, ConcatDataset
from exp.TimeSeriesDataset import TimeSeriesDataset
from data_provider.sg_dataset import ProxyPowerDataset
from sklearn.model_selection import train_test_split
import pandas as pd
import os
import time
import warnings
import numpy as np

warnings.filterwarnings('ignore')


class Exp_SG_Long_Term_Forecast(Exp_Basic):
    def __init__(self, args):
        super(Exp_SG_Long_Term_Forecast, self).__init__(args)

    def _build_model(self):
        model = self.model_dict[self.args.model].Model(self.args).float()

        total_params = 0
        for param in model.parameters():
            total_params += param.numel()

        print("Total parameters:", total_params)

        if self.args.use_multi_gpu and self.args.use_gpu:
            model = nn.DataParallel(model, device_ids=self.args.device_ids)
        return model

    def _get_data(self):
        root_path = self.args.root_path
        data_path = self.args.data_path
        file_path = os.path.join(root_path, data_path)

        # 检查数据类型，支持Excel、CSV和增强数据集
        if 'enhanced_weather_electricity_data' in data_path and data_path.endswith('.csv'):
            # 使用增强数据集
            from data_provider.sg_dataset import EnhancedProxyPowerDataset

            full_dataset = EnhancedProxyPowerDataset(
                data_path=file_path,
                seq_len=self.args.seq_len,
                label_len=self.args.label_len,
                pred_len=self.args.pred_len,
                target_col=getattr(self.args, 'target', '代理购电'),
                features=self.args.features,
                scale=True,
                feature_selection=getattr(self.args, 'feature_selection', 'important')
            )

        elif data_path.endswith('.xlsx') or data_path.endswith('.xls'):
            # 使用原有的ProxyPowerDataset处理Excel文件
            full_dataset = ProxyPowerDataset(
                data_path=file_path,
                seq_len=self.args.seq_len,
                label_len=self.args.label_len,
                pred_len=self.args.pred_len,
                target_col=getattr(self.args, 'target', '代理购电'),
                features=self.args.features,
                scale=True
            )

            # 划分数据集索引
            total_len = len(full_dataset)
            train_len = int(0.7 * total_len)
            valid_len = int(0.15 * total_len)
            test_len = total_len - train_len - valid_len

            train_indices = list(range(0, train_len))
            valid_indices = list(range(train_len, train_len + valid_len))
            test_indices = list(range(train_len + valid_len, total_len))

            # 创建子数据集
            train_dataset = Subset(full_dataset, train_indices)
            valid_dataset = Subset(full_dataset, valid_indices)
            test_dataset = Subset(full_dataset, test_indices)

        else:
            # 原有的CSV处理逻辑
            df = pd.read_csv(file_path)
            data = df.values
            print("元数据：", data.shape)

            # 划分数据集
            train_data, temp_data = train_test_split(data, test_size=0.2, shuffle=False)
            valid_data, test_data = train_test_split(temp_data, test_size=0.5, shuffle=False)

            # 构建 Dataset
            train_dataset = TimeSeriesDataset(train_data, timestamps=self.args.seq_len + self.args.label_len + self.args.pred_len, seq_len=self.args.seq_len, label_len=self.args.label_len, pred_len=self.args.pred_len, step=1)
            valid_dataset = TimeSeriesDataset(valid_data, timestamps=self.args.seq_len + self.args.label_len + self.args.pred_len, seq_len=self.args.seq_len, label_len=self.args.label_len, pred_len=self.args.pred_len, step=1)
            test_dataset = TimeSeriesDataset(test_data, timestamps=self.args.seq_len + self.args.label_len + self.args.pred_len, seq_len=self.args.seq_len, label_len=self.args.label_len, pred_len=self.args.pred_len, step=1)

            # 设置 step（如果 args.step 不为 1）
            if self.args.step != 1:
                train_dataset.step = self.args.step
                valid_dataset.step = self.args.step
                test_dataset.step = self.args.step

        # 构建 DataLoader
        train_loader = DataLoader(train_dataset, batch_size=self.args.batch_size, shuffle=True, drop_last=True)
        valid_loader = DataLoader(valid_dataset, batch_size=self.args.batch_size, shuffle=False, drop_last=True)
        test_loader = DataLoader(test_dataset, batch_size=self.args.batch_size, shuffle=False, drop_last=True)

        print("训练集：", len(train_dataset))
        print("验证集：", len(valid_dataset))
        print("测试集：", len(test_dataset))

        return train_loader, valid_loader, test_loader

    def _select_optimizer(self):
        model_optim = optim.Adam(self.model.parameters(), lr=self.args.learning_rate)
        return model_optim

    def _select_criterion(self):
        criterion = nn.MSELoss()
        return criterion

    def vali(self, vali_loader, criterion):
        total_loss = []
        self.model.eval()
        with torch.no_grad():
            for i, (batch_x, batch_y, batch_x_mark, batch_y_mark) in enumerate(vali_loader):
                batch_x = batch_x.float().to(self.device)
                batch_y = batch_y.float()

                batch_x_mark = batch_x_mark.float().to(self.device)
                batch_y_mark = batch_y_mark.float().to(self.device)

                # decoder input
                dec_inp = torch.zeros_like(batch_y[:, -self.args.pred_len:, :]).float()
                dec_inp = torch.cat([batch_y[:, :self.args.label_len, :], dec_inp], dim=1).float().to(self.device)
                # encoder - decoder
                if self.args.use_amp:
                    with torch.cuda.amp.autocast():
                        if self.args.output_attention:
                            outputs = self.model(batch_x)[0]
                        else:
                            outputs = self.model(batch_x)
                else:
                    if self.args.output_attention:
                        outputs = self.model(batch_x)[0]
                    else:
                        outputs = self.model(batch_x)
                f_dim = -1 if self.args.features == 'MS' else 0
                outputs = outputs[:, -self.args.pred_len:, f_dim:]

                if self.args.load_m == 0:
                    batch_y = batch_y[:, -self.args.pred_len:, f_dim:].to(self.device)
                else:
                    batch_y = batch_y[:, self.args.label_len:, f_dim:].squeeze(-1).to(self.device)
                    batch_size, timestamp_num = batch_y.shape
                    batch_y = torch.max(batch_y.reshape(batch_size, 30, -1), dim=-1)[0].unsqueeze(-1)

                pred = outputs.detach().cpu()
                true = batch_y.detach().cpu()

                loss = criterion(pred, true)

                total_loss.append(loss)
        total_loss = np.average(total_loss)
        self.model.train()
        return total_loss

    def train(self, setting):

        train_loader, vali_loader, test_loader = self._get_data()
        print("开始训练！")
        print("训练集数量：", len(train_loader))
        print("验证集数量：", len(vali_loader))
        print("测试集数量：", len(test_loader))

        path = os.path.join(self.args.checkpoints, setting)
        if not os.path.exists(path):
            os.makedirs(path)

        time_now = time.time()

        train_steps = len(train_loader)
        early_stopping = EarlyStopping(patience=self.args.patience, verbose=True)

        model_optim = self._select_optimizer()
        criterion = self._select_criterion()

        if self.args.use_amp:
            scaler = torch.cuda.amp.GradScaler()

        for epoch in range(self.args.train_epochs):
            iter_count = 0
            train_loss = []

            self.model.train()
            epoch_time = time.time()
            for i, (batch_x, batch_y, batch_x_mark, batch_y_mark) in enumerate(train_loader):
                iter_count += 1
                model_optim.zero_grad()
                batch_x = batch_x.float().to(self.device)
                batch_y = batch_y.float().to(self.device)
                batch_x_mark = batch_x_mark.float().to(self.device)
                batch_y_mark = batch_y_mark.float().to(self.device)

                # decoder input
                dec_inp = torch.zeros_like(batch_y[:, -self.args.pred_len:, :]).float()
                dec_inp = torch.cat([batch_y[:, :self.args.label_len, :], dec_inp], dim=1).float().to(self.device)

                # encoder - decoder
                if self.args.use_amp:
                    with torch.cuda.amp.autocast():
                        if self.args.output_attention:
                            outputs = self.model(batch_x)[0]
                        else:
                            outputs = self.model(batch_x)

                        f_dim = -1 if self.args.features == 'MS' else 0
                        outputs = outputs[:, -self.args.pred_len:, f_dim:]
                        batch_y = batch_y[:, -self.args.pred_len:, f_dim:].to(self.device)
                        loss = criterion(outputs, batch_y)
                        train_loss.append(loss.item())
                else:
                    if self.args.output_attention:
                        outputs = self.model(batch_x)[0]
                    else:
                        outputs = self.model(batch_x)

                    f_dim = -1 if self.args.features == 'MS' else 0
                    outputs = outputs[:, -self.args.pred_len:, f_dim:]

                    if self.args.load_m == 0:
                        batch_y = batch_y[:, -self.args.pred_len:, f_dim:].to(self.device)
                    else:
                        batch_y = batch_y[:, self.args.label_len:, f_dim:].squeeze(-1).to(self.device)
                        batch_size, timestamp_num = batch_y.shape
                        batch_y = torch.max(batch_y.reshape(batch_size, 30, -1), dim=-1)[0].unsqueeze(-1)

                    loss = criterion(outputs, batch_y)
                    train_loss.append(loss.item())

                if (i + 1) % 100 == 0:
                    print("\titers: {0}, epoch: {1} | loss: {2:.7f}".format(i + 1, epoch + 1, loss.item()))
                    speed = (time.time() - time_now) / iter_count
                    left_time = speed * ((self.args.train_epochs - epoch) * train_steps - i)
                    print('\tspeed: {:.4f}s/iter; left time: {:.4f}s'.format(speed, left_time))
                    iter_count = 0
                    time_now = time.time()

                if self.args.use_amp:
                    scaler.scale(loss).backward()
                    scaler.step(model_optim)
                    scaler.update()
                else:
                    loss.backward()
                    model_optim.step()
            print("Epoch: {} cost time: {}".format(epoch + 1, time.time() - epoch_time))
            train_loss = np.average(train_loss)
            vali_loss = self.vali(vali_loader, criterion)
            # test_loss = self.vali(test_loader, criterion)

            # print("Epoch: {0}, Steps: {1} | Train Loss: {2:.7f} Vali Loss: {3:.7f} Test Loss: {4:.7f}".format(
            #     epoch + 1, train_steps, train_loss, vali_loss, test_loss))
            print("Epoch: {0}, Steps: {1} | Train Loss: {2:.7f} Vali Loss: {3:.7f}".format(
                epoch + 1, train_steps, train_loss, vali_loss))
            early_stopping(vali_loss, self.model, path)
            if early_stopping.early_stop:
                print("Early stopping")
                break

            adjust_learning_rate(model_optim, epoch + 1, self.args)

        best_model_path = path + '/' + 'checkpoint.pth'
        # self.model.load_state_dict(torch.load(best_model_path))
        self.model.load_state_dict(torch.load(best_model_path, map_location=torch.device('cpu')))
        self.model.to(self.device)

        return self.model


    def test(self, setting, test=0):
        self.test_(setting, 'train', test)
        self.test_(setting, 'valid', test)
        self.test_(setting, 'test', test)

    def test_(self, setting, dataset='test', test=0):
        train_loader, vali_loader, test_loader = self._get_data()
        if test:
            print('loading model')
            # self.model.load_state_dict(torch.load(os.path.join('./checkpoints/' + setting, 'checkpoint.pth')))
            self.model.load_state_dict(torch.load(os.path.join('./checkpoints/' + setting, 'checkpoint.pth'), map_location=torch.device('cpu')))
            self.model.to(self.device)


        preds = []
        trues = []
        xs = []

        folder_path = './test_results/' + setting + '/'
        if not os.path.exists(folder_path):
            os.makedirs(folder_path)

        # 获取数据集用于反标准化
        if dataset == 'test':
            data_loader = test_loader
            data_set = test_loader.dataset
        elif dataset == 'valid':
            data_loader = vali_loader
            data_set = vali_loader.dataset
        else:
            data_loader = train_loader
            data_set = train_loader.dataset

        # 如果数据集被包装在Subset中，获取底层数据集
        if hasattr(data_set, 'dataset'):
            original_dataset = data_set.dataset
        else:
            original_dataset = data_set

        loader = None
        if dataset == 'train': loader = train_loader
        if dataset == 'valid': loader = vali_loader
        if dataset == 'test': loader = test_loader

        self.model.eval()
        with torch.no_grad():
            for i, (batch_x, batch_y, batch_x_mark, batch_y_mark) in tqdm(enumerate(loader)):
                xs.append(batch_x)
                batch_x = batch_x.float().to(self.device)
                batch_y = batch_y.float().to(self.device)

                batch_x_mark = batch_x_mark.float().to(self.device)
                batch_y_mark = batch_y_mark.float().to(self.device)

                # decoder input
                dec_inp = torch.zeros_like(batch_y[:, -self.args.pred_len:, :]).float()
                dec_inp = torch.cat([batch_y[:, :self.args.label_len, :], dec_inp], dim=1).float().to(self.device)
                # encoder - decoder
                if self.args.use_amp:
                    with torch.cuda.amp.autocast():
                        if self.args.output_attention:
                            outputs = self.model(batch_x)[0]
                        else:
                            outputs = self.model(batch_x)
                else:
                    if self.args.output_attention:
                        outputs = self.model(batch_x)[0]

                    else:
                        outputs = self.model(batch_x)

                f_dim = -1 if self.args.features == 'MS' else 0
                outputs = outputs[:, -self.args.pred_len:, f_dim:]

                if self.args.load_m == 0:
                    batch_y = batch_y[:, -self.args.pred_len:, f_dim:].to(self.device)
                else:
                    batch_y = batch_y[:, self.args.label_len:, f_dim:].squeeze(-1).to(self.device)
                    batch_size, timestamp_num = batch_y.shape
                    batch_y = torch.max(batch_y.reshape(batch_size, 30, -1), dim=-1)[0].unsqueeze(-1)

                outputs = outputs.detach().cpu().numpy()
                batch_y = batch_y.detach().cpu().numpy()
                pred = outputs
                true = batch_y

                preds.append(pred)
                trues.append(true)
                if i % 20 == 0:
                    input = batch_x.detach().cpu().numpy()

                    # 反标准化数据以显示真实数值
                    if hasattr(original_dataset, 'inverse_transform') and hasattr(original_dataset, 'scale') and original_dataset.scale:
                        try:
                            # 获取特征模式和数据列信息
                            features = getattr(self.args, 'features', 'S')

                            if features == 'S':
                                # 单变量模式：直接反标准化
                                true_denorm = original_dataset.inverse_transform(true[0, :, -1].reshape(-1, 1)).flatten()
                                pred_denorm = original_dataset.inverse_transform(pred[0, :, -1].reshape(-1, 1)).flatten()
                                input_denorm = original_dataset.inverse_transform(input[0, :, -1].reshape(-1, 1)).flatten()
                            elif features == 'MS':
                                # 多变量输入单变量输出：需要找到目标列
                                target_col = getattr(self.args, 'target', '代理购电')
                                if hasattr(original_dataset, 'data_columns'):
                                    target_idx = original_dataset.data_columns.index(target_col)

                                    # 创建完整维度的数组进行反标准化
                                    n_features = len(original_dataset.data_columns)

                                    # 处理true数据
                                    true_full = np.zeros((true.shape[1], n_features))
                                    true_full[:, target_idx] = true[0, :, -1]
                                    true_denorm = original_dataset.inverse_transform(true_full)[:, target_idx]

                                    # 处理pred数据
                                    pred_full = np.zeros((pred.shape[1], n_features))
                                    pred_full[:, target_idx] = pred[0, :, -1]
                                    pred_denorm = original_dataset.inverse_transform(pred_full)[:, target_idx]

                                    # 处理input数据
                                    input_full = np.zeros((input.shape[1], n_features))
                                    input_full[:, target_idx] = input[0, :, -1]
                                    input_denorm = original_dataset.inverse_transform(input_full)[:, target_idx]
                                else:
                                    # 回退到单变量处理
                                    true_denorm = original_dataset.inverse_transform(true[0, :, -1].reshape(-1, 1)).flatten()
                                    pred_denorm = original_dataset.inverse_transform(pred[0, :, -1].reshape(-1, 1)).flatten()
                                    input_denorm = original_dataset.inverse_transform(input[0, :, -1].reshape(-1, 1)).flatten()
                            else:
                                # 多变量模式：使用所有特征
                                true_denorm = original_dataset.inverse_transform(true[0, :, :])[:, -1]
                                pred_denorm = original_dataset.inverse_transform(pred[0, :, :])[:, -1]
                                input_denorm = original_dataset.inverse_transform(input[0, :, :])[:, -1]

                            gt = np.concatenate((input_denorm, true_denorm), axis=0)
                            pd = np.concatenate((input_denorm, pred_denorm), axis=0)

                            # 验证反标准化结果是否合理
                            if np.any(gt < 0) or np.any(pd < 0):
                                print(f"警告: 反标准化后出现负值，使用原始标准化数据")
                                print(f"GT范围: {gt.min():.4f} - {gt.max():.4f}")
                                print(f"PD范围: {pd.min():.4f} - {pd.max():.4f}")
                                # 回退到原始数据
                                gt = np.concatenate((input[0, :, -1], true[0, :, -1]), axis=0)
                                pd = np.concatenate((input[0, :, -1], pred[0, :, -1]), axis=0)
                            else:
                                print(f"反标准化成功 - GT范围: {gt.min():.2f} - {gt.max():.2f}")

                        except Exception as e:
                            print(f"反标准化失败: {str(e)}, 使用原始数据")
                            gt = np.concatenate((input[0, :, -1], true[0, :, -1]), axis=0)
                            pd = np.concatenate((input[0, :, -1], pred[0, :, -1]), axis=0)
                    else:
                        # 如果没有反标准化方法，使用原始数据
                        gt = np.concatenate((input[0, :, -1], true[0, :, -1]), axis=0)
                        pd = np.concatenate((input[0, :, -1], pred[0, :, -1]), axis=0)

                    visual(gt, pd, os.path.join(folder_path, str(i) + '.pdf'))

        preds = np.array(preds)
        trues = np.array(trues)
        xs = torch.cat(xs, dim=0).numpy()

        preds = preds.reshape(-1, preds.shape[-2], preds.shape[-1])
        trues = trues.reshape(-1, trues.shape[-2], trues.shape[-1])

        print('test shape:', preds.shape, trues.shape)

        # result save
        folder_path = './results/' + setting + '/'
        if not os.path.exists(folder_path):
            os.makedirs(folder_path)

        mae, mse, rmse, mape, mspe = metric(preds, trues)
        print('mse:{}, mae:{}, mape:{}, mspe:{}'.format(mse, mae, mape, mspe))

        f = open("result_long_term_forecast.txt", 'a')
        f.write(setting + "  \n")
        f.write('mse:{}, mae:{}, mape:{}, mspe:{}'.format(mse, mae, mape, mspe))
        f.write('\n')
        f.write('\n')
        f.close()

        # 保存评估指标为CSV格式
        from utils.tools import save_metrics_csv, extract_model_id_from_setting

        metrics_dict = {
            'MAE': mae,
            'MSE': mse,
            'RMSE': rmse,
            'MAPE': mape,
            'MSPE': mspe
        }

        model_id = extract_model_id_from_setting(setting)
        metrics_csv_path = folder_path + f'metrics_{model_id}.csv'
        save_metrics_csv(metrics_dict, metrics_csv_path)
        print(f"评估指标已保存到: {metrics_csv_path}")

        # 保持原有的npy格式以兼容现有代码
        np.save(folder_path + 'metrics.npy', np.array([mae, mse, rmse, mape, mspe]))
        np.save(folder_path + f'{dataset}_pred.npy', preds)
        np.save(folder_path + f'{dataset}_true.npy', trues)
        np.save(folder_path + f'{dataset}_x.npy', xs)

        return
