# 代理购电时间序列预测系统

基于PatchTST模型的代理购电数据预测系统，支持多种时间尺度的预测任务。

## 项目概述

本项目是对原有PatchTST时间序列预测框架的扩展，专门用于处理"代理购电.xlsx"数据集。主要特点：

- 🔥 基于最新的PatchTST (Patch Time Series Transformer) 模型
- 📊 支持Excel文件直接加载和处理
- 🎯 专门针对"代理购电"列进行预测
- ⏰ 支持多种预测时间尺度（1天、7天、30天）
- 📈 提供完整的训练、测试和推理流程
- 🎨 包含数据可视化和结果分析功能

## 数据集要求

- **文件格式**: Excel (.xlsx)
- **文件位置**: `./data/代理购电.xlsx`
- **必需列**: 
  - `日期`: 日期信息（格式：YYYYMMDD）
  - `代理购电`: 目标预测列
- **可选列**: `居民`, `农业` 等其他特征列

## 环境配置

### 依赖包安装

```bash
pip install torch==1.7.1
pip install pandas==1.5.3
pip install numpy==1.23.5
pip install matplotlib==3.7.0
pip install scikit-learn==1.2.2
pip install openpyxl  # 用于读取Excel文件
```

### 验证环境

运行测试脚本验证环境配置：

```bash
python test_data_loading.py
```

该脚本会执行以下测试：
1. Excel文件加载测试
2. 数据集创建测试
3. DataLoader测试
4. 数据可视化
5. 模型兼容性测试

## 使用指南

### 1. 数据准备

确保 `./data/代理购电.xlsx` 文件存在且格式正确。

### 2. 模型训练

运行训练脚本：

```bash
python run_proxy_power.py
```

该脚本会自动训练三个不同预测长度的模型：
- **1天预测模型**: 预测未来1天的代理购电量
- **7天预测模型**: 预测未来7天的代理购电量  
- **30天预测模型**: 预测未来30天的代理购电量

训练参数：
- 输入序列长度：96天（约3个月历史数据）
- 模型维度：512
- 注意力头数：8
- 训练轮数：50
- 学习率：0.0001

### 3. 模型推理

训练完成后，使用推理脚本进行预测：

```bash
python inference_proxy_power.py
```

推理脚本功能：
- 交互式选择预测模式（1天/7天/30天）
- 自动加载对应的训练好的模型
- 生成预测结果和可视化图表
- 保存预测结果为CSV文件

### 4. 结果文件

训练和推理过程会生成以下文件：

**模型权重文件**（保存在 `./checkpoints/` 目录）：
- `long_term_sg_forecast_proxy_power_1d_PatchTST_*/checkpoint.pth`
- `long_term_sg_forecast_proxy_power_7d_PatchTST_*/checkpoint.pth`
- `long_term_sg_forecast_proxy_power_30d_PatchTST_*/checkpoint.pth`

**预测结果文件**：
- `prediction_proxy_power_1d.csv`: 1天预测结果
- `prediction_proxy_power_7d.csv`: 7天预测结果
- `prediction_proxy_power_30d.csv`: 30天预测结果

**可视化图表**：
- `prediction_proxy_power_1d.png`: 1天预测图表
- `prediction_proxy_power_7d.png`: 7天预测图表
- `prediction_proxy_power_30d.png`: 30天预测图表
- `data_analysis.png`: 数据分析图表

## 代码结构

### 新增/修改的文件

1. **data_provider/sg_dataset.py** (修改)
   - 新增 `ProxyPowerDataset` 类
   - 支持Excel文件加载
   - 数据标准化和时间特征提取

2. **exp/exp_sg_long_term_forecasting.py** (修改)
   - 修改 `_get_data` 方法
   - 支持Excel和CSV文件自动识别
   - 集成新的数据集类

3. **run_proxy_power.py** (新增)
   - 专门的训练脚本
   - 自动化多模型训练流程
   - 参数配置和错误处理

4. **inference_proxy_power.py** (新增)
   - 推理和预测脚本
   - 结果可视化
   - 交互式操作界面

5. **test_data_loading.py** (新增)
   - 数据加载测试脚本
   - 环境验证
   - 数据分析和可视化

### 核心配置参数

```python
# 数据配置
seq_len = 96        # 输入序列长度（天）
label_len = 24      # 标签长度
pred_len = 1/7/30   # 预测长度（天）
features = 'S'      # 单变量预测
target = '代理购电'  # 目标列名

# 模型配置
d_model = 512       # 模型维度
n_heads = 8         # 注意力头数
e_layers = 2        # 编码器层数
d_ff = 2048         # 前馈网络维度
dropout = 0.1       # Dropout率

# 训练配置
batch_size = 32/16/8  # 批次大小（根据预测长度调整）
learning_rate = 0.0001
train_epochs = 50
patience = 10       # 早停耐心值
```

## 模型特点

### PatchTST优势
- **Patch机制**: 将时间序列分割成patches，提高计算效率
- **Transformer架构**: 利用自注意力机制捕获长期依赖关系
- **多尺度预测**: 支持不同时间尺度的预测任务
- **高精度**: 在多个时间序列预测基准上表现优异

### 数据处理特点
- **自动标准化**: 使用StandardScaler进行数据标准化
- **时间特征**: 自动提取月、日、星期等时间特征
- **灵活分割**: 支持训练/验证/测试集自动分割
- **批量处理**: 高效的批量数据加载和处理

## 故障排除

### 常见问题

1. **Excel文件读取失败**
   - 检查文件路径是否正确
   - 确保安装了 `openpyxl` 包
   - 验证Excel文件格式和列名

2. **CUDA内存不足**
   - 减小batch_size参数
   - 使用CPU训练（设置use_gpu=False）

3. **模型权重文件不存在**
   - 确保先运行训练脚本
   - 检查checkpoints目录是否存在

4. **中文显示问题**
   - 安装中文字体
   - 检查matplotlib配置

### 性能优化建议

1. **GPU加速**: 使用CUDA GPU可显著提升训练速度
2. **批次大小**: 根据显存大小调整batch_size
3. **数据预处理**: 预先处理数据可减少训练时间
4. **模型剪枝**: 可适当减少模型层数以提升速度

## 扩展功能

### 支持多变量预测
修改 `features` 参数：
- `'S'`: 单变量预测（仅预测代理购电）
- `'M'`: 多变量预测（预测所有变量）
- `'MS'`: 多变量输入，单变量输出

### 添加新的预测长度
在配置文件中添加新的预测长度和对应的模型路径。

### 集成其他模型
框架支持集成其他时间序列预测模型，如Transformer、Autoformer等。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue到项目仓库
- 发送邮件至项目维护者

---

**注意**: 本项目基于PatchTST论文实现，请在使用时遵循相关的学术引用规范。
