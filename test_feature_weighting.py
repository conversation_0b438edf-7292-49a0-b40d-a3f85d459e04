#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for correlation-based feature weighting implementation
"""

import pandas as pd
import numpy as np
from data_provider.sg_dataset import ProxyPowerDataset
from feature_selector import auto_select_features_for_ms_mode

def test_feature_weighting():
    """Test the correlation-based feature weighting functionality"""
    print("Testing Correlation-Based Feature Weighting")
    print("=" * 60)
    
    try:
        # Test 1: Feature selection with correlation coefficients and weights
        print("1. Testing feature selection with correlation extraction...")
        
        # Get data columns from integrated weather data
        data_path = 'data/整合天气数据_代理购电_20250716_094327.xlsx'
        df = pd.read_excel(data_path)
        
        # Get numerical columns only
        exclude_cols = ['日期', '天气状况', '风向风力']
        all_data_columns = []
        for col in df.columns:
            if col not in exclude_cols and pd.api.types.is_numeric_dtype(df[col]):
                all_data_columns.append(col)
        
        print(f"Available data columns: {all_data_columns}")
        
        # Test feature selection for 代理购电
        target = '代理购电'
        print(f"\n--- Testing feature selection for {target} ---")
        
        result = auto_select_features_for_ms_mode(
            target_electricity_type=target,
            all_data_columns=all_data_columns,
            correlation_results_dir='feature_analysis_results'
        )
        
        print(f"✓ Feature selection completed")
        print(f"  Selected features: {result['selected_features']}")
        print(f"  Selection method: {result['selection_method']}")
        print(f"  Final mode: {result['final_mode']}")
        
        # Check if correlation coefficients and weights are available
        if 'correlation_coefficients' in result:
            print(f"  Correlation coefficients: {result['correlation_coefficients']}")
        if 'feature_weights' in result:
            print(f"  Feature weights: {result['feature_weights']}")
            
            # Verify weights sum to 1.0
            if result['feature_weights']:
                weight_sum = sum(result['feature_weights'].values())
                print(f"  Weight sum: {weight_sum:.6f} (should be ~1.0)")
        
        # Test 2: Dataset creation with feature weighting
        print(f"\n2. Testing dataset creation with feature weighting...")
        
        dataset = ProxyPowerDataset(
            data_path=data_path,
            seq_len=96,
            label_len=24,
            pred_len=7,
            target_col=target,
            features='MS',
            scale=True,
            auto_feature_selection=True
        )
        
        print(f"✓ Dataset created successfully")
        print(f"  Data shape: {dataset.data.shape}")
        print(f"  Features mode: {dataset.features}")
        print(f"  Selected columns: {dataset.data_columns}")
        
        # Check if feature weighting was applied
        if hasattr(dataset, 'feature_selection_result') and dataset.feature_selection_result:
            selection_result = dataset.feature_selection_result
            if 'feature_weights' in selection_result and selection_result['feature_weights']:
                print(f"  ✓ Feature weighting applied")
                print(f"  Weights: {selection_result['feature_weights']}")
            else:
                print(f"  ⚠ No feature weights found")
        
        # Test 3: Data loading and validation
        print(f"\n3. Testing data loading with weighted features...")
        
        try:
            sample = dataset[0]
            print(f"✓ Sample data loaded successfully")
            print(f"  Input shape: {sample[0].shape}")
            print(f"  Output shape: {sample[1].shape}")
            print(f"  Input data type: {sample[0].dtype}")
            print(f"  Output data type: {sample[1].dtype}")
            
            # Check for any unusual values
            if hasattr(sample[0], 'numpy'):
                input_data = sample[0].numpy()
            else:
                input_data = sample[0]
            print(f"  Input data range: [{input_data.min():.3f}, {input_data.max():.3f}]")
            print(f"  Input data mean: {input_data.mean():.3f}")
            print(f"  Input data std: {input_data.std():.3f}")

            # Check if there are any NaN or infinite values
            if np.isnan(input_data).any():
                print(f"  ⚠ Warning: Input contains NaN values")
            if np.isinf(input_data).any():
                print(f"  ⚠ Warning: Input contains infinite values")
            
        except Exception as e:
            print(f"✗ Data loading failed: {str(e)}")
            return False
        
        # Test 4: Compare with non-weighted version
        print(f"\n4. Testing comparison with non-weighted version...")
        
        dataset_no_weights = ProxyPowerDataset(
            data_path=data_path,
            seq_len=96,
            label_len=24,
            pred_len=7,
            target_col=target,
            features='MS',
            scale=True,
            auto_feature_selection=False  # Disable auto feature selection
        )
        
        print(f"✓ Non-weighted dataset created")
        print(f"  Data shape: {dataset_no_weights.data.shape}")
        print(f"  Selected columns: {dataset_no_weights.data_columns}")
        
        # Compare data statistics
        sample_no_weights = dataset_no_weights[0]
        input_no_weights = sample_no_weights[0].numpy()
        
        print(f"  Non-weighted data range: [{input_no_weights.min():.3f}, {input_no_weights.max():.3f}]")
        print(f"  Non-weighted data mean: {input_no_weights.mean():.3f}")
        print(f"  Non-weighted data std: {input_no_weights.std():.3f}")
        
        print(f"\n✓ All feature weighting tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Feature weighting test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_weight_calculation():
    """Test the weight calculation logic separately"""
    print("\n" + "=" * 60)
    print("Testing Weight Calculation Logic")
    print("=" * 60)
    
    try:
        from feature_selector import AutoFeatureSelector
        
        # Create test correlation data
        test_correlations = {
            '高温_数值': 0.429,
            '低温_数值': 0.412,
            '高温': 0.343
        }
        
        selector = AutoFeatureSelector()
        weights = selector.calculate_correlation_weights(test_correlations)
        
        print(f"Test correlations: {test_correlations}")
        print(f"Calculated weights: {weights}")
        
        # Verify weights sum to 1.0
        weight_sum = sum(weights.values())
        print(f"Weight sum: {weight_sum:.6f}")
        
        # Verify weights are proportional to absolute correlations
        abs_corrs = {k: abs(v) for k, v in test_correlations.items()}
        total_abs_corr = sum(abs_corrs.values())
        expected_weights = {k: v / total_abs_corr for k, v in abs_corrs.items()}
        
        print(f"Expected weights: {expected_weights}")
        
        # Check if calculated weights match expected
        for feature in weights:
            calculated = weights[feature]
            expected = expected_weights[feature]
            diff = abs(calculated - expected)
            print(f"  {feature}: calculated={calculated:.6f}, expected={expected:.6f}, diff={diff:.6f}")
            
            if diff > 1e-6:
                print(f"  ✗ Weight calculation error for {feature}")
                return False
        
        print(f"✓ Weight calculation test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Weight calculation test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("Correlation-Based Feature Weighting Test")
    print("=" * 80)
    
    # Test 1: Feature weighting functionality
    test1_passed = test_feature_weighting()
    
    # Test 2: Weight calculation logic
    test2_passed = test_weight_calculation()
    
    # Summary
    print("\n" + "=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)
    print(f"Feature Weighting Functionality: {'✓ PASSED' if test1_passed else '✗ FAILED'}")
    print(f"Weight Calculation Logic: {'✓ PASSED' if test2_passed else '✗ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 ALL TESTS PASSED! Correlation-based feature weighting is working correctly.")
        return True
    else:
        print("\n❌ SOME TESTS FAILED. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
