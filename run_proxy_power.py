import argparse
import os
import torch
import random
import numpy as np
from exp.exp_sg_long_term_forecasting import Exp_SG_Long_Term_Forecast
from config_manager import ConfigManager

def set_seed(seed=2023):
    """设置随机种子"""
    random.seed(seed)
    torch.manual_seed(seed)
    np.random.seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = True

def create_args(target='农业', features='S', pred_len=1, batch_size=32, **kwargs):
    """创建参数配置 - 支持自动化配置"""

    # 验证和获取配置
    try:
        ConfigManager.validate_target_in_data(target)

        # 对于MS模式，先进行特征选择以获得正确的维度配置
        selected_features = None
        if features == 'MS' and kwargs.get('auto_feature_selection', True):
            try:
                from feature_selector import auto_select_features_for_ms_mode
                import pandas as pd

                # 获取数据列信息 - 使用集成天气数据进行特征选择
                data_path = ConfigManager.get_data_file_path('MS', True)
                df = pd.read_excel(data_path)

                # 只使用数值列进行特征选择
                exclude_cols = ['日期', '天气状况', '风向风力']
                all_data_columns = []
                for col in df.columns:
                    if col not in exclude_cols and pd.api.types.is_numeric_dtype(df[col]):
                        all_data_columns.append(col)

                # 执行特征选择
                feature_selection_result = auto_select_features_for_ms_mode(
                    target_electricity_type=target,
                    all_data_columns=all_data_columns,
                    correlation_results_dir=kwargs.get('correlation_results_dir', 'feature_analysis_results'),
                    correlation_file_path=kwargs.get('correlation_file_path', None)
                )

                selected_features = feature_selection_result['selected_features']

                # 如果建议回退到S模式，更新features
                if feature_selection_result['final_mode'] == 'S':
                    features = 'S'
                    print(f"根据特征选择结果，将模式从MS更改为S")

            except Exception as e:
                print(f"Warning: 特征选择失败: {str(e)}")
                print("使用默认的MS模式配置")

        dim_config = ConfigManager.auto_configure_dimensions(features, target, selected_features=selected_features)
        model_id = ConfigManager.generate_model_id(target, pred_len, features)
        display_names = ConfigManager.get_display_names(target, features)
    except Exception as e:
        print(f"配置错误: {str(e)}")
        raise

    parser = argparse.ArgumentParser(description=f'{display_names["target_description"]}')

    # 基本配置
    parser.add_argument('--task_name', type=str, default='long_term_sg_forecast')
    parser.add_argument('--is_training', type=int, default=1)
    parser.add_argument('--model_id', type=str, default=model_id)
    parser.add_argument('--model', type=str, default='PatchTST')

    # 数据配置
    parser.add_argument('--data', type=str, default='proxy_power')
    parser.add_argument('--root_path', type=str, default='./data/')
    # 根据特征模式和自动特征选择确定数据文件
    data_file = ConfigManager.get_data_file_path(features, kwargs.get('auto_feature_selection', True))
    data_filename = os.path.basename(data_file)
    parser.add_argument('--data_path', type=str, default=data_filename)
    parser.add_argument('--features', type=str, default=features)
    parser.add_argument('--target', type=str, default=target)
    parser.add_argument('--freq', type=str, default='d')
    parser.add_argument('--checkpoints', type=str, default='./checkpoints/')

    # 特征选择配置
    parser.add_argument('--auto_feature_selection', type=bool, default=True)
    parser.add_argument('--correlation_file_path', type=str, default=None)
    parser.add_argument('--correlation_results_dir', type=str, default='feature_analysis_results')

    # 预测配置
    parser.add_argument('--seq_len', type=int, default=96)
    parser.add_argument('--label_len', type=int, default=24)
    parser.add_argument('--pred_len', type=int, default=pred_len)

    # 自动配置的模型维度
    parser.add_argument('--enc_in', type=int, default=dim_config['enc_in'])
    parser.add_argument('--dec_in', type=int, default=dim_config['dec_in'])
    parser.add_argument('--c_out', type=int, default=dim_config['c_out'])
    parser.add_argument('--d_model', type=int, default=512)
    parser.add_argument('--n_heads', type=int, default=8)
    parser.add_argument('--e_layers', type=int, default=2)
    parser.add_argument('--d_layers', type=int, default=1)
    parser.add_argument('--d_ff', type=int, default=2048)
    parser.add_argument('--factor', type=int, default=3)
    parser.add_argument('--dropout', type=float, default=0.1)
    parser.add_argument('--embed', type=str, default='timeF')
    parser.add_argument('--activation', type=str, default='gelu')
    parser.add_argument('--output_attention', action='store_true', default=False)
    parser.add_argument('--distil', action='store_false', default=True)

    # 训练配置
    parser.add_argument('--num_workers', type=int, default=0)
    parser.add_argument('--itr', type=int, default=1)
    parser.add_argument('--train_epochs', type=int, default=50)
    parser.add_argument('--batch_size', type=int, default=batch_size)
    parser.add_argument('--patience', type=int, default=10)
    parser.add_argument('--learning_rate', type=float, default=0.0001)
    parser.add_argument('--des', type=str, default='Exp')
    parser.add_argument('--loss', type=str, default='MSE')
    parser.add_argument('--lradj', type=str, default='type1')
    parser.add_argument('--use_amp', action='store_true', default=False)

    # GPU配置
    parser.add_argument('--use_gpu', type=bool, default=True)
    parser.add_argument('--gpu', type=int, default=0)
    parser.add_argument('--use_multi_gpu', action='store_true', default=False)
    parser.add_argument('--devices', type=str, default='0')

    # 其他配置
    parser.add_argument('--step', type=int, default=1)
    parser.add_argument('--load_m', type=int, default=0)

    # 应用额外的配置参数
    for key, value in kwargs.items():
        if hasattr(parser, 'add_argument'):
            parser.add_argument(f'--{key}', default=value)

    args = parser.parse_args([])
    args.use_gpu = True if torch.cuda.is_available() and args.use_gpu else False

    # 存储配置信息供后续使用
    args._config_info = {
        'target_config': ConfigManager.get_target_config(target),
        'dim_config': dim_config,
        'display_names': display_names
    }

    return args

def train_model(target='农业', features='S', pred_len=1, batch_size=32):
    """训练模型 - 支持自动化配置"""

    # 获取显示名称
    display_names = ConfigManager.get_display_names(target, features)
    model_id = ConfigManager.generate_model_id(target, pred_len, features)

    print(f"\n{'='*60}")
    print(f"开始训练模型: {model_id}")
    print(f"目标变量: {display_names['target_name']}")
    print(f"特征模式: {display_names['feature_mode']}")
    print(f"预测长度: {pred_len} 天")
    print(f"批次大小: {batch_size}")
    print(f"{'='*60}")

    # 创建参数
    args = create_args(
        target=target,
        features=features,
        pred_len=pred_len,
        batch_size=batch_size
    )

    # 创建实验
    exp = Exp_SG_Long_Term_Forecast(args)

    # 生成设置字符串
    setting = '{}_{}_{}_{}_ft{}_sl{}_ll{}_pl{}_dm{}_nh{}_el{}_dl{}_df{}_fc{}_eb{}_dt{}_{}_{}'.format(
        args.task_name, args.model_id, args.model, args.data, args.features,
        args.seq_len, args.label_len, args.pred_len, args.d_model, args.n_heads,
        args.e_layers, args.d_layers, args.d_ff, args.factor, args.embed,
        args.distil, args.des, 0)

    print(f'>>>>>>>开始训练: {setting}>>>>>>>>>>>>>>>>>>>>>>>>>>>')
    exp.train(setting)

    print(f'>>>>>>>开始测试: {setting}<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<')
    exp.test(setting)

    if torch.cuda.is_available():
        torch.cuda.empty_cache()

    print(f"模型 {model_id} 训练完成！")
    return setting

def main(target='农业', features='S'):
    """主函数 - 支持自动化配置"""

    # 获取显示名称和验证配置
    try:
        display_names = ConfigManager.get_display_names(target, features)
        ConfigManager.validate_target_in_data(target)
    except Exception as e:
        print(f"配置错误: {str(e)}")
        print("可用的目标变量:", list(ConfigManager.TARGET_CONFIGS.keys()))
        print("可用的特征模式: S, M, MS")
        return

    print(f"{display_names['target_description']}模型训练")
    print("="*60)

    # 打印配置摘要
    ConfigManager.print_configuration_summary(target, features, 1)

    # 设置随机种子
    set_seed(2023)

    # 检查数据文件
    data_file = ConfigManager.DATA_FILE_PATH
    if not os.path.exists(data_file):
        print(f"错误：数据文件 {data_file} 不存在！")
        return

    print(f"数据文件: {data_file}")
    print("开始训练多个预测模型...")

    # 训练配置列表：(预测长度, 批次大小)
    training_configs = [
        # 1天预测
        (15, 16),    # 7天预测
        (20, 8)    # 30天预测
        # (50, 8)     # 50天预测
        # (56, 8)     # 56天预测
    ]

    trained_models = []

    for pred_len, batch_size in training_configs:
        try:
            setting = train_model(target, features, pred_len, batch_size)
            model_id = ConfigManager.generate_model_id(target, pred_len, features)
            trained_models.append((model_id, setting))
            print(f"✓ {model_id} 训练成功")
        except Exception as e:
            model_id = ConfigManager.generate_model_id(target, pred_len, features)
            print(f"✗ {model_id} 训练失败: {str(e)}")
            continue

    print("\n" + "="*60)
    print("训练总结:")
    print(f"成功训练 {len(trained_models)} 个模型:")
    for model_id, setting in trained_models:
        print(f"  - {model_id}: {setting}")

    print("\n模型权重保存在 ./checkpoints/ 目录下")
    print("可以使用这些模型进行预测！")


if __name__ == '__main__':
    # 支持命令行参数
    import sys
    if len(sys.argv) >= 2:
        target = sys.argv[1]
        features = sys.argv[2] if len(sys.argv) >= 3 else 'S'
        main(target, features)
    else:
        # 默认配置
        main()
