"""
统一预测脚本 - 支持多目标变量和特征模式的自动化预测
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from inference_proxy_power import main as inference_main
from config_manager import ConfigManager


def add_real_data_to_monthly_summary(detailed_df, df_raw, target, display_names, prediction_start_date):
    """
    添加真实数据补充当月电量，计算完整的当月总和

    Args:
        detailed_df: 包含预测数据的详细DataFrame
        df_raw: 原始数据DataFrame
        target: 目标变量名
        display_names: 显示名称字典
        prediction_start_date: 预测开始日期

    Returns:
        enhanced_monthly_summary: 包含真实数据补充的月度汇总
    """
    try:
        # 获取预测开始日期的年月
        start_year = prediction_start_date.year
        start_month = prediction_start_date.month

        # 转换原始数据的日期格式
        df_raw['日期_parsed'] = pd.to_datetime(df_raw['日期'].astype(str), format='%Y-%m-%d')
        df_raw['年'] = df_raw['日期_parsed'].dt.year
        df_raw['月'] = df_raw['日期_parsed'].dt.month
        df_raw['日'] = df_raw['日期_parsed'].dt.day

        # 获取当月的真实数据（预测开始日期之前的数据）
        current_month_real = df_raw[
            (df_raw['年'] == start_year) &
            (df_raw['月'] == start_month) &
            (df_raw['日期_parsed'] < prediction_start_date)
        ]

        # 计算当月真实数据的总和
        if len(current_month_real) > 0:
            real_sum = current_month_real[target].sum()
            real_days = len(current_month_real)
        else:
            real_sum = 0
            real_days = 0

        # 获取当月的预测数据
        current_month_pred = detailed_df[
            (detailed_df['年'] == start_year) &
            (detailed_df['月'] == start_month)
        ]

        if len(current_month_pred) > 0:
            pred_sum = current_month_pred[f'预测{display_names["target_name"]}'].sum()
            pred_days = len(current_month_pred)
        else:
            pred_sum = 0
            pred_days = 0

        # 计算完整的当月总和
        complete_month_sum = real_sum + pred_sum
        total_days = real_days + pred_days

        # 创建增强的月度汇总
        enhanced_summary = []

        # 添加当月完整数据
        if total_days > 0:
            enhanced_summary.append({
                '年': start_year,
                '月': start_month,
                f'真实{display_names["target_name"]}': real_sum,
                f'预测{display_names["target_name"]}': pred_sum,
                f'完整月度{display_names["target_name"]}总和': complete_month_sum,
                '真实天数': real_days,
                '预测天数': pred_days,
                '总天数': total_days,
                '数据类型': '当月完整数据'
            })

        # 添加其他月份的纯预测数据
        other_months = detailed_df[
            ~((detailed_df['年'] == start_year) & (detailed_df['月'] == start_month))
        ].groupby(['年', '月']).agg({
            f'预测{display_names["target_name"]}': 'sum',
            '预测天数': 'count'
        }).reset_index()

        for _, row in other_months.iterrows():
            enhanced_summary.append({
                '年': row['年'],
                '月': row['月'],
                f'真实{display_names["target_name"]}': 0,
                f'预测{display_names["target_name"]}': row[f'预测{display_names["target_name"]}'],
                f'完整月度{display_names["target_name"]}总和': row[f'预测{display_names["target_name"]}'],
                '真实天数': 0,
                '预测天数': row['预测天数'],
                '总天数': row['预测天数'],
                '数据类型': '纯预测数据'
            })

        enhanced_monthly_summary = pd.DataFrame(enhanced_summary)

        print(f"\n真实数据补充结果:")
        print(f"当月({start_year}年{start_month}月)真实数据: {real_sum:,.0f} ({real_days}天)")
        print(f"当月({start_year}年{start_month}月)预测数据: {pred_sum:,.0f} ({pred_days}天)")
        print(f"当月({start_year}年{start_month}月)完整总和: {complete_month_sum:,.0f} ({total_days}天)")

        return enhanced_monthly_summary

    except Exception as e:
        print(f"添加真实数据时出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return pd.DataFrame()


def execute_prediction_with_length(target, features, pred_len):
    """
    执行指定长度的预测，不需要用户交互

    Args:
        target: 目标变量名
        features: 特征模式
        pred_len: 预测长度
    """
    try:
        from inference_proxy_power import PowerForecastInference, get_model_configs
        import pandas as pd

        # 获取显示名称和验证配置
        display_names = ConfigManager.get_display_names(target, features)
        ConfigManager.validate_target_in_data(target)

        print(f"{display_names['target_description']}推理")
        print("="*60)

        # 根据特征模式确定数据文件路径
        data_path = ConfigManager.get_data_file_path(features, auto_feature_selection=True)

        # 检查数据文件
        if not os.path.exists(data_path):
            print(f"错误：数据文件 {data_path} 不存在！")
            return None

        # 获取配置
        configs = get_model_configs(target, features, pred_len)

        # 检查模型文件
        if not configs.weight_path:
            print(f"错误：找不到{display_names['target_name']}的模型权重文件")
            print("请先运行训练脚本训练模型！")
            return None

        # 创建推理器
        inference = PowerForecastInference(configs)

        # 生成文件名
        file_names = ConfigManager.generate_file_names(target, pred_len, features)

        # 执行预测并可视化
        prediction = inference.visualize_prediction(data_path, file_names['png'])

        # 保存预测结果
        result_df = pd.DataFrame({
            '预测天数': range(1, len(prediction) + 1),
            f'预测{display_names["target_name"]}': prediction
        })

        result_df.to_csv(file_names['csv'], index=False, encoding='utf-8-sig')

        print(f"\n{display_names['target_name']}预测结果:")
        print(result_df)
        print(f"\n预测结果已保存: {file_names['csv']}")

        # 返回预测信息，供后续处理使用
        return {
            'pred_len': pred_len,
            'csv_path': file_names['csv'],
            'target': target,
            'features': features
        }

    except Exception as e:
        print(f"预测过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


def process_longterm_prediction_with_monthly_summary(target, features, prediction_csv_path, pred_len):
    """
    处理长期预测(>=15天)的日期识别和月度汇总功能

    Args:
        target: 目标变量名
        features: 特征模式
        prediction_csv_path: 预测结果CSV文件路径
        pred_len: 预测长度
    """
    try:
        # 读取预测结果
        prediction_df = pd.read_csv(prediction_csv_path, encoding='utf-8-sig')

        # 读取原始数据获取最后日期
        # 根据特征模式确定数据文件路径
        data_path = ConfigManager.get_data_file_path(features, auto_feature_selection=True)
        if not os.path.exists(data_path):
            print(f"错误：数据文件 {data_path} 不存在！")
            return

        # 读取原始数据
        if data_path.endswith('.xlsx'):
            df_raw = pd.read_excel(data_path)
        else:
            df_raw = pd.read_csv(data_path)

        # 获取最后日期
        if '日期' not in df_raw.columns:
            print("错误：数据文件中没有找到'日期'列！")
            return

        # 转换日期格式
        dates = pd.to_datetime(df_raw['日期'].astype(str), format='%Y-%m-%d')
        last_date = dates.iloc[-1]

        # 生成未来预测天数的日期
        future_dates = pd.date_range(start=last_date + timedelta(days=1),
                                   periods=pred_len, freq='D')

        # 创建包含日期的预测结果DataFrame
        display_names = ConfigManager.get_display_names(target, features)
        prediction_column = f'预测{display_names["target_name"]}'

        detailed_df = pd.DataFrame({
            '日期': future_dates,
            '年': future_dates.year,
            '月': future_dates.month,
            '日': future_dates.day,
            '预测天数': range(1, pred_len + 1),
            prediction_column: prediction_df[prediction_column].values
        })

        # 添加真实数据补充当月电量
        enhanced_monthly_summary = add_real_data_to_monthly_summary(
            detailed_df, df_raw, target, display_names, future_dates[0]
        )

        # 按月汇总预测数据（保持原有格式用于兼容性）
        monthly_summary = detailed_df.groupby(['年', '月']).agg({
            prediction_column: 'sum',
            '预测天数': 'count'
        }).reset_index()

        monthly_summary.rename(columns={
            prediction_column: f'月度{display_names["target_name"]}预测总和',
            '预测天数': '预测天数'
        }, inplace=True)

        # 生成新的文件名
        file_names = ConfigManager.generate_file_names(target, pred_len, features)
        base_name = file_names['csv'].replace('.csv', '')

        # 保存详细的日数据（包含日期）
        daily_detailed_path = f"{base_name}_daily_with_dates.csv"
        # detailed_df.to_csv(daily_detailed_path, index=False, encoding='utf-8-sig')

        # 保存月度汇总数据（原有格式）
        monthly_summary_path = f"{base_name}_monthly_summary.csv"
        # monthly_summary.to_csv(monthly_summary_path, index=False, encoding='utf-8-sig')

        # 保存增强的月度汇总数据（包含真实数据补充）
        enhanced_summary_path = f"{base_name}_enhanced_monthly_summary.csv"
        if not enhanced_monthly_summary.empty:
            enhanced_monthly_summary.to_csv(enhanced_summary_path, index=False, encoding='utf-8-sig')

        print(f"\n{pred_len}天长期预测特殊处理完成:")
        print(f"预测起始日期: {future_dates[0].strftime('%Y-%m-%d')}")
        print(f"预测结束日期: {future_dates[-1].strftime('%Y-%m-%d')}")
        print(f"详细日数据已保存: {daily_detailed_path}")
        print(f"月度汇总数据已保存: {monthly_summary_path}")
        if not enhanced_monthly_summary.empty:
            print(f"增强月度汇总已保存: {enhanced_summary_path}")

        print(f"\n增强月度汇总结果:")
        if not enhanced_monthly_summary.empty:
            print(enhanced_monthly_summary)
        else:
            print("无增强汇总数据")

        # 显示当月完整数据（如果存在）
        if not enhanced_monthly_summary.empty:
            current_month_data = enhanced_monthly_summary[
                enhanced_monthly_summary['数据类型'] == '当月完整数据'
            ]
            if len(current_month_data) > 0:
                row = current_month_data.iloc[0]
                complete_column = f'完整月度{display_names["target_name"]}总和'
                print(f"\n当月({row['年']}年{row['月']}月)完整总和: {row[complete_column]:,.0f}")
                real_column = f'真实{display_names["target_name"]}'
                pred_column = f'预测{display_names["target_name"]}'
                print(f"  - 真实数据: {row[real_column]:,.0f} ({row['真实天数']}天)")
                print(f"  - 预测数据: {row[pred_column]:,.0f} ({row['预测天数']}天)")

        return daily_detailed_path, monthly_summary_path

    except Exception as e:
        print(f"处理长期预测时出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None


def predict_all_available():
    """预测所有可用的模型"""
    print("查找并预测所有可用的模型")
    print("=" * 60)
    
    if not os.path.exists("./checkpoints/"):
        print("错误：checkpoints目录不存在，请先训练模型！")
        return
    
    # 获取所有可用的目标变量和特征模式
    targets = list(ConfigManager.TARGET_CONFIGS.keys())
    features_modes = ['S', 'MS']
    pred_lengths = [1, 7, 15, 20, 30, 50, 56]
    
    available_models = []
    
    # 检查哪些模型可用
    for target in targets:
        for features in features_modes:
            for pred_len in pred_lengths:
                weight_file = ConfigManager.find_weight_file(target, pred_len, features)
                if weight_file:
                    available_models.append((target, features, pred_len))
    
    if not available_models:
        print("没有找到可用的模型，请先训练模型！")
        return
    
    print(f"找到 {len(available_models)} 个可用模型:")
    for i, (target, features, pred_len) in enumerate(available_models):
        display_names = ConfigManager.get_display_names(target, features)
        print(f"  {i+1}. {display_names['target_name']} ({features}) - {pred_len}天预测")
    
    # 让用户选择
    try:
        choice = int(input(f"\n请选择要预测的模型 (1-{len(available_models)}): ")) - 1
        if 0 <= choice < len(available_models):
            target, features, pred_len = available_models[choice]
            print(f"\n开始预测: {target} ({features}) - {pred_len}天")

            # 直接执行指定长度的预测
            prediction_info = execute_prediction_with_length(target, features, pred_len)

            # 检查是否为长期预测(>=15天)，如果是则进行特殊处理
            if prediction_info and prediction_info['pred_len'] >= 15:
                pred_len = prediction_info['pred_len']
                print(f"\n检测到{pred_len}天长期预测，开始进行日期识别和月度汇总...")
                daily_path, monthly_path = process_longterm_prediction_with_monthly_summary(
                    target, features, prediction_info['csv_path'], pred_len
                )
                if daily_path and monthly_path:
                    print(f"✓ {pred_len}天长期预测特殊处理完成")
                else:
                    print(f"✗ {pred_len}天长期预测特殊处理失败")
        else:
            print("无效的选择！")
    except ValueError:
        print("请输入有效的数字！")


def predict_specific_target(target, features='S'):
    """预测指定目标变量的模型"""
    try:
        ConfigManager.validate_target_in_data(target)
        display_names = ConfigManager.get_display_names(target, features)
        print(f"开始预测 {display_names['target_name']} 模型 (特征模式: {features})")

        # 执行预测
        prediction_info = inference_main(target, features)

        # 检查是否为长期预测(>=15天)，如果是则进行特殊处理
        if prediction_info and prediction_info['pred_len'] >= 15:
            pred_len = prediction_info['pred_len']
            print(f"\n检测到{pred_len}天长期预测，开始进行日期识别和月度汇总...")
            daily_path, monthly_path = process_longterm_prediction_with_monthly_summary(
                target, features, prediction_info['csv_path'], pred_len
            )
            if daily_path and monthly_path:
                print(f"✓ {pred_len}天长期预测特殊处理完成")
            else:
                print(f"✗ {pred_len}天长期预测特殊处理失败")

    except Exception as e:
        print(f"✗ {target} 模型预测失败: {str(e)}")


def list_available_models():
    """列出所有可用的模型"""
    print("可用的模型列表")
    print("=" * 60)
    
    if not os.path.exists("./checkpoints/"):
        print("checkpoints目录不存在，没有可用的模型。")
        return
    
    targets = list(ConfigManager.TARGET_CONFIGS.keys())
    features_modes = ['S', 'MS']
    pred_lengths = [1, 7, 30, 50]
    
    found_any = False
    
    for target in targets:
        target_config = ConfigManager.get_target_config(target)
        print(f"\n{target_config.chinese_name} ({target}):")
        
        target_has_models = False
        for features in features_modes:
            feature_info = ConfigManager.FEATURE_MODES[features]
            models_for_feature = []
            
            for pred_len in pred_lengths:
                weight_file = ConfigManager.find_weight_file(target, pred_len, features)
                if weight_file:
                    models_for_feature.append(f"{pred_len}天")
                    found_any = True
                    target_has_models = True
            
            if models_for_feature:
                print(f"  {features} ({feature_info['name']}): {', '.join(models_for_feature)}")
        
        if not target_has_models:
            print("  无可用模型")
    
    if not found_any:
        print("\n没有找到任何可用的模型，请先运行训练脚本。")


def show_usage():
    """显示使用说明"""
    print("电力预测模型推理脚本")
    print("=" * 50)
    print("使用方法:")
    print("  python predict_models.py                    # 交互式选择模型预测")
    print("  python predict_models.py list               # 列出所有可用模型")
    print("  python predict_models.py <目标变量>          # 预测指定目标变量")
    print("  python predict_models.py <目标变量> <特征模式> # 预测指定目标和特征模式")
    print()
    print("可用的目标变量:")
    for target, config in ConfigManager.TARGET_CONFIGS.items():
        print(f"  {target} - {config.description}")
    print()
    print("可用的特征模式:")
    for mode, info in ConfigManager.FEATURE_MODES.items():
        print(f"  {mode} - {info['description']}")
    print()
    print("示例:")
    print("  python predict_models.py 代理购电 S")
    print("  python predict_models.py 居民 MS")
    print("  python predict_models.py 农业")


def main():
    """主函数"""
    if len(sys.argv) == 1:
        # 没有参数，交互式选择
        predict_all_available()
    elif len(sys.argv) == 2:
        if sys.argv[1] in ['-h', '--help', 'help']:
            show_usage()
        elif sys.argv[1] == 'list':
            list_available_models()
        else:
            # 预测指定目标变量，默认单变量模式
            target = sys.argv[1]
            predict_specific_target(target, 'S')
    elif len(sys.argv) == 3:
        # 预测指定目标变量和特征模式
        target = sys.argv[1]
        features = sys.argv[2]
        predict_specific_target(target, features)
    else:
        print("参数错误！")
        show_usage()


if __name__ == '__main__':
    main()
