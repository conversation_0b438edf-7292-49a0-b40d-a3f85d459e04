#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for weather data cleaning and feature selection
"""

import pandas as pd
from data_provider.sg_dataset import ProxyPowerDataset

def test_weather_data_cleaning():
    """Test the weather data cleaning functionality"""
    print("Testing Weather Data Cleaning and Feature Selection")
    print("=" * 60)
    
    try:
        # Test with integrated weather data
        print("1. Testing ProxyPowerDataset with integrated weather data...")
        
        dataset = ProxyPowerDataset(
            data_path='data/整合天气数据_代理购电_20250716_094327.xlsx',
            seq_len=96,
            label_len=24,
            pred_len=7,
            target_col='代理购电',
            features='MS',
            scale=True,
            auto_feature_selection=True
        )
        
        print(f"✓ Dataset created successfully")
        print(f"  Data shape: {dataset.data.shape}")
        print(f"  Features mode: {dataset.features}")
        print(f"  Selected columns: {dataset.data_columns}")
        
        # Check data types
        print(f"\n2. Checking data types after cleaning...")
        for col in dataset.data_columns:
            if col in dataset.df_raw.columns:
                dtype = dataset.df_raw[col].dtype
                print(f"  {col}: {dtype}")
                
                # Check for any non-numeric values
                if dtype == 'object':
                    print(f"    Warning: {col} is still object type!")
                    sample_values = dataset.df_raw[col].head().tolist()
                    print(f"    Sample values: {sample_values}")
        
        # Test data loading
        print(f"\n3. Testing data loading...")
        try:
            sample = dataset[0]
            print(f"✓ Sample data loaded successfully")
            print(f"  Input shape: {sample[0].shape}")
            print(f"  Output shape: {sample[1].shape}")
            print(f"  Input data type: {sample[0].dtype}")
            print(f"  Output data type: {sample[1].dtype}")
            
            # Check for NaN values
            if pd.isna(sample[0]).any():
                print(f"  Warning: Input contains NaN values")
            if pd.isna(sample[1]).any():
                print(f"  Warning: Output contains NaN values")
                
        except Exception as e:
            print(f"✗ Data loading failed: {str(e)}")
            return False
        
        # Test feature selection results
        if hasattr(dataset, 'feature_selection_result') and dataset.feature_selection_result:
            result = dataset.feature_selection_result
            print(f"\n4. Feature selection results:")
            print(f"  Selection method: {result['selection_method']}")
            print(f"  Final mode: {result['final_mode']}")
            print(f"  Selected features: {result['selected_features']}")
            
            # Verify all selected features are numerical
            for feature in result['selected_features']:
                if feature in dataset.df_raw.columns:
                    dtype = dataset.df_raw[feature].dtype
                    if not pd.api.types.is_numeric_dtype(dataset.df_raw[feature]):
                        print(f"  ✗ Warning: {feature} is not numeric ({dtype})")
                    else:
                        print(f"  ✓ {feature} is numeric ({dtype})")
        
        print(f"\n✓ Weather data cleaning test completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Weather data cleaning test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_numerical_columns_extraction():
    """Test the numerical columns extraction"""
    print("\n" + "=" * 60)
    print("Testing Numerical Columns Extraction")
    print("=" * 60)
    
    try:
        # Load raw data
        df = pd.read_excel('data/整合天气数据_代理购电_20250716_094327.xlsx')
        print(f"Original data shape: {df.shape}")
        print(f"Original columns: {df.columns.tolist()}")
        
        # Create dataset to trigger data cleaning
        dataset = ProxyPowerDataset(
            data_path='data/整合天气数据_代理购电_20250716_094327.xlsx',
            seq_len=96,
            label_len=24,
            pred_len=7,
            target_col='代理购电',
            features='M',  # Use M mode to get all numerical columns
            scale=True,
            auto_feature_selection=False  # Disable auto selection to see all columns
        )
        
        print(f"\nAfter cleaning:")
        print(f"  Cleaned data shape: {dataset.df_raw.shape}")
        print(f"  Selected columns: {dataset.data_columns}")
        
        # Check data types of all columns
        print(f"\nData types after cleaning:")
        for col in dataset.df_raw.columns:
            dtype = dataset.df_raw[col].dtype
            is_numeric = pd.api.types.is_numeric_dtype(dataset.df_raw[col])
            print(f"  {col}: {dtype} (numeric: {is_numeric})")
        
        return True
        
    except Exception as e:
        print(f"✗ Numerical columns extraction test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("Weather Data Cleaning and Feature Selection Test")
    print("=" * 80)
    
    # Test 1: Weather data cleaning
    test1_passed = test_weather_data_cleaning()
    
    # Test 2: Numerical columns extraction
    test2_passed = test_numerical_columns_extraction()
    
    # Summary
    print("\n" + "=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)
    print(f"Weather Data Cleaning: {'✓ PASSED' if test1_passed else '✗ FAILED'}")
    print(f"Numerical Columns Extraction: {'✓ PASSED' if test2_passed else '✗ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 ALL TESTS PASSED! Weather data cleaning is working correctly.")
        return True
    else:
        print("\n❌ SOME TESTS FAILED. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
