#!/usr/bin/env python3
"""
时间序列月度预测快速运行脚本
用于快速执行ARIMA/SARIMAX预测分析
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from arima_monthly_predictor import ARIMAMonthlyPredictor

def quick_prediction(target='代理购电', months=12, data_file=None, model_type='ARIMA'):
    """快速预测函数"""

    print(f"开始{model_type}月度预测 - {target}")
    print("=" * 50)

    # 设置数据文件路径
    if data_file is None:
        data_file = './data/龙泉代理购电.xlsx'

    try:
        # 创建预测器
        predictor = ARIMAMonthlyPredictor(
            data_path=data_file,
            target_column=target,
            model_type=model_type
        )

        # 运行完整分析
        predictions = predictor.run_complete_analysis(
            prediction_months=months,
            evaluation=True  # 跳过评估以加快速度
        )

        print("\n预测完成！")
        print(f"模型类型: {model_type}")
        print(f"目标变量: {target}")
        print(f"预测期间: {predictions.index[0].strftime('%Y-%m')} 到 {predictions.index[-1].strftime('%Y-%m')}")
        print(f"平均预测值: {predictions['预测值'].mean():.2f}")

        # 显示前几个月的预测结果
        print("\n前6个月预测详情:")
        display_predictions = predictions.head(6).copy()
        display_predictions.index = display_predictions.index.strftime('%Y-%m')
        for idx, row in display_predictions.iterrows():
            print(f"{idx}: {row['预测值']:.2f} (区间: {row['下限']:.2f} - {row['上限']:.2f})")

        return predictions

    except Exception as e:
        print(f"预测过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='时间序列月度电力预测')
    parser.add_argument('--target', '-t', default='代理购电',
                       choices=['代理购电', '居民', '农业'],
                       help='预测目标变量')
    parser.add_argument('--months', '-m', type=int, default=12,
                       help='预测月数')
    parser.add_argument('--data', '-d', default=None,
                       help='数据文件路径')
    parser.add_argument('--model', '-M', default='ARIMA',
                       choices=['ARIMA', 'SARIMAX'],
                       help='模型类型: ARIMA或SARIMAX')

    args = parser.parse_args()

    # 执行预测
    predictions = quick_prediction(
        target=args.target,
        months=args.months,
        data_file=args.data,
        model_type=args.model
    )

    if predictions is not None:
        print(f"\n预测结果已保存到 ARIMA/results/ 目录")
        print("可以查看生成的图表和CSV文件")

if __name__ == "__main__":
    # 如果没有命令行参数，使用默认设置
    if len(sys.argv) == 1:
        print("使用默认设置运行预测...")
        quick_prediction()
    else:
        main()
