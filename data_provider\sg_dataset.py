import pandas as pd
import numpy as np
import os.path as osp
import datetime
import pickle
import torch
import matplotlib.pyplot as plt
from tqdm import tqdm
from torch.utils.data import Dataset, random_split, Subset, ConcatDataset
import os
from sklearn.preprocessing import StandardScaler
plt.style.use('seaborn')

data_path = '/data/tushihao/SOG'


ORG_CODE2index = {33401400110:0, 33401400153:1, 33401400114:2, 33401400150:3}


class TimeSeriesDataset(Dataset):
    def __init__(self, data, timestamps, seq_len, label_len, pred_len, step=1):
        self.data = data
        self.timestamps = timestamps
        self.seq_len = seq_len
        self.label_len = label_len
        self.pred_len = pred_len
        self.step = step
        self.total_len = seq_len + pred_len
        
    def __len__(self):
        return (len(self.data) - self.total_len) // self.step + 1

    def __getitem__(self, idx):
        start = idx * self.step
        end = start + self.seq_len
        pred_end = end + self.pred_len
        
        # 序列数据
        batch_x = self.data[start:end]
        batch_y = self.data[end:pred_end]
        
        # 时间戳特征（简化处理）
        batch_x_mark = self.timestamps[start:end]  # 时间戳特征
        batch_y_mark = self.timestamps[end:pred_end]
        
        return batch_x, batch_y, batch_x_mark, batch_y_mark


class ZBPowerSubDataset(Dataset):
    def __init__(self, ID, ORG_CODE, DATES, OT, DAY_TYPES, WEATHERS, SEQ_LEN, LABEL_LEN, PRED_LEN):
        self.ID = ID
        self.ORG_CODE = list(ORG_CODE)[0]
        self.DATES = DATES
        self.OT = OT

        self.DAY_TYPES = DAY_TYPES
        self.WEATHERS = WEATHERS

        self.SEQ_LEN = SEQ_LEN
        self.LABEL_LEN = LABEL_LEN
        self.PRED_LEN = PRED_LEN
        self.WINDOW_LENGTH = SEQ_LEN + PRED_LEN

        self.DATES = pd.to_datetime(self.DATES, format='%Y-%m-%d %H:%M:%S')
        self.LEN = len(self.OT) - self.WINDOW_LENGTH + 1

        self.begin = self.DATES[0]
        self.end = self.DATES[self.LEN - 1]

        self.DATES = np.vstack([np.array([i.year, i.month, i.day]) for i in self.DATES])

        self.step = 1

    def __len__(self):
        length = (len(self.OT) - self.WINDOW_LENGTH + 1) // self.step
        return length

    def __getitem__(self, index):
        s_begin = index * self.step
        s_end = s_begin + self.SEQ_LEN
        r_begin = s_end - self.LABEL_LEN
        r_end = r_begin + self.LABEL_LEN + self.PRED_LEN

        seq_x = torch.Tensor(self.OT[s_begin:s_end]).reshape(-1, 1)
        seq_y = torch.Tensor(self.OT[r_begin:r_end]).reshape(-1, 1)
        seq_x_mark = torch.Tensor(self.DATES[s_begin:s_end])
        seq_y_mark = torch.Tensor(self.DATES[r_begin:r_end])
        data_types = torch.Tensor(self.DAY_TYPES[s_begin:s_end])

        # return seq_x+1, seq_y+1, ORG_CODE2index[self.ORG_CODE], data_types
        return seq_x+1, seq_y+1, seq_x_mark, seq_y_mark


class ZBPowerDataset(object):
    def __init__(self, zb_user_power, split_ratio=[0.6, 0.2, 0.2], SEQ_LEN=96, LABEL_LEN=48, PRED_LEN=30):
        self.IDS = zb_user_power['cust_id'].unique()
        self.ALL_DATES = zb_user_power.groupby(['cust_id'])['data_date'].apply(list)
        self.ALL_ORG_CODE = zb_user_power.groupby(['cust_id'])['mgt_org_code'].apply(set)

        self.ALL_OT = zb_user_power.groupby(['cust_id'])['energy'].apply(list)
        self.ALL_DAY_TYPES = zb_user_power.groupby(['cust_id'])['day_type'].apply(list)
        self.ALL_WEATHERS = zb_user_power.groupby(['cust_id'])['weather_val'].apply(list)

        self.begin = datetime.datetime.strptime('2022-05-01', '%Y-%m-%d')
        self.end = datetime.datetime.strptime('2023-05-31', '%Y-%m-%d')
        self.TIMESPAN = pd.date_range(start=self.begin, end=self.end, freq='1D')[:-(SEQ_LEN + PRED_LEN)]

        self.split_ratio = split_ratio
        self.train_valid_boundary = self.TIMESPAN[int(self.split_ratio[0] * len(self.TIMESPAN))]
        self.valid_test_boundary = self.TIMESPAN[int((self.split_ratio[0] + self.split_ratio[1]) * len(self.TIMESPAN))]

        self.SEQ_LEN = SEQ_LEN
        self.LABEL_LEN = LABEL_LEN
        self.PRED_LEN = PRED_LEN

        self.WINDOW_LENGTH = SEQ_LEN + PRED_LEN

    def get_dataset(self):
        train_ds = []
        valid_ds = []
        test_ds = []
        for ID in self.IDS:
            sub_ds = ZBPowerSubDataset(ID=ID,
                                       ORG_CODE=self.ALL_ORG_CODE[ID],
                                       DATES=self.ALL_DATES[ID],
                                       OT=self.ALL_OT[ID],
                                       DAY_TYPES=self.ALL_DAY_TYPES[ID],
                                       WEATHERS=self.ALL_WEATHERS[ID],
                                       SEQ_LEN=self.SEQ_LEN,
                                       LABEL_LEN=self.LABEL_LEN,
                                       PRED_LEN=self.PRED_LEN)
            length = len(sub_ds)
            indexes = list(range(length))

            if sub_ds.begin < self.train_valid_boundary:
                train_end = sub_ds.end if sub_ds.end < self.train_valid_boundary else self.train_valid_boundary

                train_indexes = list(range((train_end - sub_ds.begin).days + 1))
                sub_train_ds = Subset(sub_ds, train_indexes)
                train_ds.append(sub_train_ds)

                # print('1', train_indexes[0], train_indexes[-1])

            if not (sub_ds.end < self.train_valid_boundary or self.valid_test_boundary <= sub_ds.begin):
                valid_begin = sub_ds.begin if sub_ds.begin > self.train_valid_boundary else self.train_valid_boundary
                valid_end = sub_ds.end if sub_ds.end < self.valid_test_boundary else self.valid_test_boundary

                valid_indexes = np.array(list(range((valid_end - valid_begin).days + 1)))[1:]
                offset = (valid_begin - sub_ds.begin).days
                sub_valid_ds = Subset(sub_ds, valid_indexes + offset)
                valid_ds.append(sub_valid_ds)

                # print('2', valid_indexes[0]+ offset, valid_indexes[-1]+ offset)

            if sub_ds.end >= self.valid_test_boundary:
                test_begin = sub_ds.begin if sub_ds.begin > self.valid_test_boundary else self.valid_test_boundary

                test_indexes = np.array(list(range((sub_ds.end - test_begin).days + 1)))[1:]
                offset = (test_begin - sub_ds.begin).days
                sub_test_ds = Subset(sub_ds, test_indexes + offset)
                test_ds.append(sub_test_ds)

                # print('3', test_indexes[0]+ offset, test_indexes[-1]+ offset)
            # break
        train_ds = ConcatDataset(train_ds)
        valid_ds = ConcatDataset(valid_ds)
        test_ds = ConcatDataset(test_ds)

        return train_ds, valid_ds, test_ds



class GBPowerSubDataset(Dataset):
    def __init__(self, ID, ORG_CODE, ORG_NAME, PRNT_ORG_CODE, PRNT_ORG_NAME, DATES, OT, SEQ_LEN, LABEL_LEN, PRED_LEN):
        self.ID = ID
        self.ORG_CODE = list(ORG_CODE)[0]
        self.ORG_NAME = list(ORG_NAME)[0]

        self.PRNT_ORG_CODE = list(PRNT_ORG_CODE)[0]
        self.PRNT_ORG_NAME = list(PRNT_ORG_NAME)[0]

        self.DATES = DATES
        self.OT = OT

        self.SEQ_LEN = SEQ_LEN
        self.LABEL_LEN = LABEL_LEN
        self.PRED_LEN = PRED_LEN
        self.WINDOW_LENGTH = SEQ_LEN + PRED_LEN

        self.DATES = pd.to_datetime(self.DATES, format='%Y-%m-%d')
        self.LEN = len(self.OT) - self.WINDOW_LENGTH + 1

        self.begin = self.DATES[0]
        self.end = self.DATES[self.LEN - 1]

        self.DATES = np.vstack([np.array([i.year, i.month, i.day]) for i in self.DATES])

        self.step = 1

    def __len__(self):
        return (len(self.OT) - self.WINDOW_LENGTH + 1) // self.step

    def __getitem__(self, index):
        s_begin = index * self.step
        s_end = s_begin + self.SEQ_LEN
        r_begin = s_end - self.LABEL_LEN
        r_end = r_begin + self.LABEL_LEN + self.PRED_LEN

        seq_x = torch.Tensor(self.OT[s_begin:s_end]).reshape(-1, 1)
        seq_y = torch.Tensor(self.OT[r_begin:r_end]).reshape(-1, 1)
        seq_x_mark = torch.Tensor(self.DATES[s_begin:s_end])
        seq_y_mark = torch.Tensor(self.DATES[r_begin:r_end])

        return seq_x+1, seq_y+1, seq_x_mark, seq_y_mark


class GBPowerDataset(object):
    def __init__(self, zb_user_power, split_ratio=[0.6, 0.2, 0.2], SEQ_LEN=96, LABEL_LEN=48, PRED_LEN=30):
        self.IDS = zb_user_power['resrc_supl_code'].unique()
        self.ALL_DATES = zb_user_power.groupby(['resrc_supl_code'])['stat_date'].apply(list)
        self.ALL_ORG_CODE = zb_user_power.groupby(['resrc_supl_code'])['mgt_org_code'].apply(set)
        self.ALL_ORG_NAME = zb_user_power.groupby(['resrc_supl_code'])['mgt_org_name'].apply(set)
        self.ALL_PRNT_ORG_CODE = zb_user_power.groupby(['resrc_supl_code'])['prnt_mgt_org_code'].apply(set)
        self.ALL_PRNT_ORG_NAME = zb_user_power.groupby(['resrc_supl_code'])['prnt_mgt_org_name'].apply(set)
        self.ALL_OT = zb_user_power.groupby(['resrc_supl_code'])['upq'].apply(list)

        self.begin = datetime.datetime.strptime('2022-05-01', '%Y-%m-%d')
        self.end = datetime.datetime.strptime('2023-05-31', '%Y-%m-%d')
        self.TIMESPAN = pd.date_range(start=self.begin, end=self.end, freq='1D')[:-(SEQ_LEN + PRED_LEN)]

        self.split_ratio = split_ratio
        self.train_valid_boundary = self.TIMESPAN[int(self.split_ratio[0] * len(self.TIMESPAN))]
        self.valid_test_boundary = self.TIMESPAN[int((self.split_ratio[0] + self.split_ratio[1]) * len(self.TIMESPAN))]

        self.SEQ_LEN = SEQ_LEN
        self.LABEL_LEN = LABEL_LEN
        self.PRED_LEN = PRED_LEN

        self.WINDOW_LENGTH = SEQ_LEN + PRED_LEN

    def get_dataset(self):
        train_ds = []
        valid_ds = []
        test_ds = []
        for ID in self.IDS:
            sub_ds = GBPowerSubDataset(ID=ID,
                                       ORG_CODE=self.ALL_ORG_CODE[ID],
                                       ORG_NAME=self.ALL_ORG_NAME[ID],
                                       PRNT_ORG_CODE=self.ALL_PRNT_ORG_CODE[ID],
                                       PRNT_ORG_NAME=self.ALL_PRNT_ORG_NAME[ID],
                                       DATES=self.ALL_DATES[ID],
                                       OT=self.ALL_OT[ID],
                                       SEQ_LEN=self.SEQ_LEN,
                                       LABEL_LEN=self.LABEL_LEN,
                                       PRED_LEN=self.PRED_LEN)

            if sub_ds.begin < self.train_valid_boundary:
                train_end = sub_ds.end if sub_ds.end < self.train_valid_boundary else self.train_valid_boundary

                train_indexes = list(range((train_end - sub_ds.begin).days + 1))
                sub_train_ds = Subset(sub_ds, train_indexes)
                train_ds.append(sub_train_ds)

                # print('1', train_indexes[0], train_indexes[-1])

            if not (sub_ds.end < self.train_valid_boundary or self.valid_test_boundary <= sub_ds.begin):
                valid_begin = sub_ds.begin if sub_ds.begin > self.train_valid_boundary else self.train_valid_boundary
                valid_end = sub_ds.end if sub_ds.end < self.valid_test_boundary else self.valid_test_boundary

                valid_indexes = np.array(list(range((valid_end - valid_begin).days + 1)))[1:]
                offset = (valid_begin - sub_ds.begin).days
                sub_valid_ds = Subset(sub_ds, valid_indexes + offset)
                valid_ds.append(sub_valid_ds)

                # print('2', valid_indexes[0]+ offset, valid_indexes[-1]+ offset)

            if sub_ds.end >= self.valid_test_boundary:
                test_begin = sub_ds.begin if sub_ds.begin > self.valid_test_boundary else self.valid_test_boundary

                test_indexes = np.array(list(range((sub_ds.end - test_begin).days + 1)))[1:]
                offset = (test_begin - sub_ds.begin).days
                sub_test_ds = Subset(sub_ds, test_indexes + offset)
                test_ds.append(sub_test_ds)

            #     print('3', test_indexes[0]+ offset, test_indexes[-1]+ offset)
            # break

        train_ds = ConcatDataset(train_ds)
        valid_ds = ConcatDataset(valid_ds)
        test_ds = ConcatDataset(test_ds)

        return train_ds, valid_ds, test_ds


class ZBLoadSubDataset(Dataset):
    def __init__(self, ENTITY_ID, CUST_ID, ORG_CODE, COMP_RTO, COLL_TYPE, TYPE, OFFSET, INST_TYPE, DATES, OT, SEQ_LEN,
                 LABEL_LEN, PRED_LEN):
        self.ENTITY_ID = ENTITY_ID
        self.CUST_ID = CUST_ID
        self.ORG_CODE = ORG_CODE
        self.COMP_RTO = COMP_RTO
        self.COLL_TYPE = COLL_TYPE
        self.TYPE = TYPE
        self.OFFSET = OFFSET
        self.INST_TYPE = INST_TYPE
        self.DATES = DATES
        self.OT = OT

        self.SEQ_LEN = SEQ_LEN
        self.LABEL_LEN = LABEL_LEN
        self.PRED_LEN = PRED_LEN
        self.WINDOW_LENGTH = SEQ_LEN + PRED_LEN

        self.OT = np.array(self.OT).reshape(-1)

        tbegin = pd.to_datetime(self.DATES[0], format='%Y-%m-%d')
        tend = pd.to_datetime(self.DATES[-1], format='%Y-%m-%d')
        self.DATES = pd.date_range(start=tbegin, end=tend + pd.Timedelta(days=1), freq='15T')[:-1]

        # print(self.OT.shape)
        # print(self.DATES)

        # if len(self.DATES) != len(self.OT):
        #     print('no equal')
        self.step = 1

        self.LEN = len(self.OT) - self.WINDOW_LENGTH + 1
        self.begin = self.DATES[0]
        self.end = self.DATES[self.LEN - 1]

        self.DATES = np.vstack([np.array([i.year, i.month, i.day, i.hour, i.minute]) for i in self.DATES])

        # print(self.OT.shape)
        # print(self.DATES.shape)

    def __len__(self):
        return (len(self.OT) - self.WINDOW_LENGTH + 1) // self.step

    def __getitem__(self, index):
        s_begin = index * self.step
        s_end = s_begin + self.SEQ_LEN
        r_begin = s_end - self.LABEL_LEN
        r_end = r_begin + self.LABEL_LEN + self.PRED_LEN

        seq_x = torch.Tensor(self.OT[s_begin:s_end]).reshape(-1, 1)
        seq_y = torch.Tensor(self.OT[r_begin:r_end]).reshape(-1, 1)
        seq_x_mark = torch.Tensor(self.DATES[s_begin:s_end])
        seq_y_mark = torch.Tensor(self.DATES[r_begin:r_end])

        return seq_x+1, seq_y+1, seq_x_mark, seq_y_mark


class ZBLoadDataset(object):
    def __init__(self, zb_load, split_ratio=[0.6, 0.2, 0.2], SEQ_LEN=96, LABEL_LEN=48, PRED_LEN=30 * 96):

        col_96_points = list(zb_load.columns)[2:2 + 96]
        values_96_points = zb_load[col_96_points].values
        zb_load = zb_load.drop(col_96_points, axis=1)
        zb_load['load'] = values_96_points.tolist()

        self.ENTITY_IDS = zb_load['data_entity_id'].unique()
        self.CUST_IDS = zb_load.groupby(['data_entity_id'])['cust_id'].apply(set)
        self.ALL_ORG_CODE = zb_load.groupby(['data_entity_id'])['mgt_org_code'].apply(set)
        self.ALL_COMP_RTO = zb_load.groupby(['data_entity_id'])['comp_rto'].apply(set)
        self.ALL_COLL_TYPE = zb_load.groupby(['data_entity_id'])['data_coll_type'].apply(set)
        self.ALL_TYPE = zb_load.groupby(['data_entity_id'])['data_type'].apply(set)
        self.ALL_OFFSET = zb_load.groupby(['data_entity_id'])['offset'].apply(set)
        self.ALL_INST_TYPE = zb_load.groupby(['data_entity_id'])['inst_type'].apply(set)
        self.ALL_DATES = zb_load.groupby(['data_entity_id'])['data_date'].apply(list)
        self.ALL_OT = zb_load.groupby(['data_entity_id'])['load'].apply(list)

        self.begin = datetime.datetime.strptime('2022-05-01', '%Y-%m-%d')
        self.end = datetime.datetime.strptime('2023-05-31', '%Y-%m-%d')
        self.TIMESPAN = pd.date_range(start=self.begin, end=self.end + pd.Timedelta(days=1), freq='15T')[
                        :-(SEQ_LEN + PRED_LEN + 1)]

        print(self.TIMESPAN[-1])

        self.split_ratio = split_ratio
        self.train_valid_boundary = self.TIMESPAN[int(self.split_ratio[0] * len(self.TIMESPAN))]
        self.valid_test_boundary = self.TIMESPAN[int((self.split_ratio[0] + self.split_ratio[1]) * len(self.TIMESPAN))]

        self.SEQ_LEN = SEQ_LEN
        self.LABEL_LEN = LABEL_LEN
        self.PRED_LEN = PRED_LEN

        self.WINDOW_LENGTH = SEQ_LEN + PRED_LEN

    def get_dataset(self):
        train_ds = []
        valid_ds = []
        test_ds = []
        for ID in tqdm(self.ENTITY_IDS):
            sub_ds = ZBLoadSubDataset(ENTITY_ID=ID,
                                      CUST_ID=self.CUST_IDS[ID],
                                      ORG_CODE=self.ALL_ORG_CODE[ID],
                                      COMP_RTO=self.ALL_COMP_RTO[ID],
                                      COLL_TYPE=self.ALL_COLL_TYPE[ID],
                                      TYPE=self.ALL_TYPE[ID],
                                      OFFSET=self.ALL_OFFSET[ID],
                                      INST_TYPE=self.ALL_INST_TYPE[ID],
                                      DATES=self.ALL_DATES[ID],
                                      OT=self.ALL_OT[ID],
                                      SEQ_LEN=self.SEQ_LEN,
                                      LABEL_LEN=self.LABEL_LEN,
                                      PRED_LEN=self.PRED_LEN)

            if sub_ds.begin < self.train_valid_boundary:
                train_end = sub_ds.end if sub_ds.end < self.train_valid_boundary else self.train_valid_boundary

                train_indexes = list(range(int((train_end - sub_ds.begin).total_seconds()) // 60 // 15 + 1))
                sub_train_ds = Subset(sub_ds, train_indexes)
                train_ds.append(sub_train_ds)

                # print('1', train_indexes[0], train_indexes[-1])

            if not (sub_ds.end < self.train_valid_boundary or self.valid_test_boundary <= sub_ds.begin):
                valid_begin = sub_ds.begin if sub_ds.begin >= self.train_valid_boundary else self.train_valid_boundary
                valid_end = sub_ds.end if sub_ds.end < self.valid_test_boundary else self.valid_test_boundary

                valid_indexes = np.array(list(range(int((valid_end - valid_begin).total_seconds()) // 60 // 15 + 1)))[
                                1:]
                offset = int((valid_begin - sub_ds.begin).total_seconds()) // 60 // 15
                sub_valid_ds = Subset(sub_ds, valid_indexes + offset)
                valid_ds.append(sub_valid_ds)

                # print('2', valid_indexes[0]+ offset, valid_indexes[-1]+ offset)

            if sub_ds.end >= self.valid_test_boundary:
                test_begin = sub_ds.begin if sub_ds.begin >= self.valid_test_boundary else self.valid_test_boundary

                test_indexes = np.array(list(range(int((sub_ds.end - test_begin).total_seconds()) // 60 // 15 + 1)))[1:]
                offset = int((test_begin - sub_ds.begin).total_seconds()) // 60 // 15
                sub_test_ds = Subset(sub_ds, test_indexes + offset)
                test_ds.append(sub_test_ds)

            #     print('3', test_indexes[0]+ offset, test_indexes[-1]+ offset)
            # break

        train_ds = ConcatDataset(train_ds)
        valid_ds = ConcatDataset(valid_ds)
        test_ds = ConcatDataset(test_ds)

        return train_ds, valid_ds, test_ds


class GBLoadSubDataset(Dataset):
    def __init__(self, ENTITY_ID, DIST_STA_ID, ORG_CODE, COMP_RTO, COLL_TYPE, TYPE, OFFSET, INST_TYPE, DATES, OT,
                 SEQ_LEN, LABEL_LEN, PRED_LEN):
        self.ENTITY_ID = ENTITY_ID
        self.DIST_STA_ID = DIST_STA_ID
        self.ORG_CODE = ORG_CODE
        self.COMP_RTO = COMP_RTO
        self.COLL_TYPE = COLL_TYPE
        self.TYPE = TYPE
        self.OFFSET = OFFSET
        self.INST_TYPE = INST_TYPE
        self.DATES = DATES
        self.OT = OT

        self.SEQ_LEN = SEQ_LEN
        self.LABEL_LEN = LABEL_LEN
        self.PRED_LEN = PRED_LEN
        self.WINDOW_LENGTH = SEQ_LEN + PRED_LEN

        self.OT = np.array(self.OT).reshape(-1)

        tbegin = pd.to_datetime(self.DATES[0], format='%Y-%m-%d')
        tend = pd.to_datetime(self.DATES[-1], format='%Y-%m-%d')
        self.DATES = pd.date_range(start=tbegin, end=tend + pd.Timedelta(days=1), freq='15T')[:-1]

        self.LEN = len(self.OT) - self.WINDOW_LENGTH + 1
        self.begin = self.DATES[0]
        self.end = self.DATES[self.LEN - 1]

        self.DATES = np.vstack([np.array([i.year, i.month, i.day, i.hour, i.minute]) for i in self.DATES])

        self.step = 1

        # print(self.OT.shape)
        # print(self.DATES.shape)

    def __len__(self):
        return (len(self.OT) - self.WINDOW_LENGTH + 1) // self.step

    def __getitem__(self, index):
        s_begin = index * self.step
        s_end = s_begin + self.SEQ_LEN
        r_begin = s_end - self.LABEL_LEN
        r_end = r_begin + self.LABEL_LEN + self.PRED_LEN

        seq_x = torch.Tensor(self.OT[s_begin:s_end]).reshape(-1, 1)
        seq_y = torch.Tensor(self.OT[r_begin:r_end]).reshape(-1, 1)
        seq_x_mark = torch.Tensor(self.DATES[s_begin:s_end])
        seq_y_mark = torch.Tensor(self.DATES[r_begin:r_end])

        return seq_x+1, seq_y+1, seq_x_mark, seq_y_mark


class GBLoadDataset(object):
    def __init__(self, load, split_ratio=[0.6, 0.2, 0.2], SEQ_LEN=96, LABEL_LEN=48, PRED_LEN=30 * 96):

        col_96_points = list(load.columns)[2:2 + 96]
        values_96_points = load[col_96_points].values
        load = load.drop(col_96_points, axis=1)
        load['load'] = values_96_points.tolist()

        self.ENTITY_IDS = load['data_entity_id'].unique()
        self.DIST_STA_IDS = load.groupby(['data_entity_id'])['dist_sta_id'].apply(set)
        self.ALL_ORG_CODE = load.groupby(['data_entity_id'])['mgt_org_code'].apply(set)
        self.ALL_COMP_RTO = load.groupby(['data_entity_id'])['comp_rto'].apply(set)
        self.ALL_COLL_TYPE = load.groupby(['data_entity_id'])['data_coll_type'].apply(set)
        self.ALL_TYPE = load.groupby(['data_entity_id'])['data_type'].apply(set)
        self.ALL_OFFSET = load.groupby(['data_entity_id'])['offset'].apply(set)
        self.ALL_INST_TYPE = load.groupby(['data_entity_id'])['inst_type'].apply(set)
        self.ALL_DATES = load.groupby(['data_entity_id'])['data_date'].apply(list)
        self.ALL_OT = load.groupby(['data_entity_id'])['load'].apply(list)

        self.begin = datetime.datetime.strptime('2022-05-01', '%Y-%m-%d')
        self.end = datetime.datetime.strptime('2023-05-31', '%Y-%m-%d')
        self.TIMESPAN = pd.date_range(start=self.begin, end=self.end + pd.Timedelta(days=1), freq='15T')[
                        :-(SEQ_LEN + PRED_LEN + 1)]

        self.split_ratio = split_ratio
        self.train_valid_boundary = self.TIMESPAN[int(self.split_ratio[0] * len(self.TIMESPAN))]
        self.valid_test_boundary = self.TIMESPAN[int((self.split_ratio[0] + self.split_ratio[1]) * len(self.TIMESPAN))]

        self.SEQ_LEN = SEQ_LEN
        self.LABEL_LEN = LABEL_LEN
        self.PRED_LEN = PRED_LEN

        self.WINDOW_LENGTH = SEQ_LEN + PRED_LEN

    def get_dataset(self):
        train_ds = []
        valid_ds = []
        test_ds = []
        for ID in tqdm(self.ENTITY_IDS):
            sub_ds = GBLoadSubDataset(ENTITY_ID=ID,
                                      DIST_STA_ID=self.DIST_STA_IDS[ID],
                                      ORG_CODE=self.ALL_ORG_CODE[ID],
                                      COMP_RTO=self.ALL_COMP_RTO[ID],
                                      COLL_TYPE=self.ALL_COLL_TYPE[ID],
                                      TYPE=self.ALL_TYPE[ID],
                                      OFFSET=self.ALL_OFFSET[ID],
                                      INST_TYPE=self.ALL_INST_TYPE[ID],
                                      DATES=self.ALL_DATES[ID],
                                      OT=self.ALL_OT[ID],
                                      SEQ_LEN=self.SEQ_LEN,
                                      LABEL_LEN=self.LABEL_LEN,
                                      PRED_LEN=self.PRED_LEN)
            if sub_ds.begin < self.train_valid_boundary:
                train_end = sub_ds.end if sub_ds.end < self.train_valid_boundary else self.train_valid_boundary

                train_indexes = list(range(int((train_end - sub_ds.begin).total_seconds()) // 60 // 15 + 1))
                sub_train_ds = Subset(sub_ds, train_indexes)
                train_ds.append(sub_train_ds)

                # print('1', train_indexes[0], train_indexes[-1])

            if not (sub_ds.end < self.train_valid_boundary or self.valid_test_boundary <= sub_ds.begin):
                valid_begin = sub_ds.begin if sub_ds.begin >= self.train_valid_boundary else self.train_valid_boundary
                valid_end = sub_ds.end if sub_ds.end < self.valid_test_boundary else self.valid_test_boundary

                valid_indexes = np.array(list(range(int((valid_end - valid_begin).total_seconds()) // 60 // 15 + 1)))[
                                1:]
                offset = int((valid_begin - sub_ds.begin).total_seconds()) // 60 // 15
                sub_valid_ds = Subset(sub_ds, valid_indexes + offset)
                valid_ds.append(sub_valid_ds)

                # print('2', valid_indexes[0]+ offset, valid_indexes[-1]+ offset)

            if sub_ds.end >= self.valid_test_boundary:
                test_begin = sub_ds.begin if sub_ds.begin >= self.valid_test_boundary else self.valid_test_boundary

                test_indexes = np.array(list(range(int((sub_ds.end - test_begin).total_seconds()) // 60 // 15 + 1)))[1:]
                offset = int((test_begin - sub_ds.begin).total_seconds()) // 60 // 15
                sub_test_ds = Subset(sub_ds, test_indexes + offset)
                test_ds.append(sub_test_ds)

            #     print('3', test_indexes[0]+ offset, test_indexes[-1]+ offset)
            # break

        train_ds = ConcatDataset(train_ds)
        valid_ds = ConcatDataset(valid_ds)
        test_ds = ConcatDataset(test_ds)

        return train_ds, valid_ds, test_ds


class AreaPowerSubDataset(Dataset):
    def __init__(self, ID, DATES, OT, DAY_TYPES, WEATHERS, SEQ_LEN, LABEL_LEN, PRED_LEN):
        self.ID = ID

        self.DATES = DATES
        self.OT = OT

        self.DAY_TYPES = DAY_TYPES
        self.WEATHERS = WEATHERS

        self.SEQ_LEN = SEQ_LEN
        self.LABEL_LEN = LABEL_LEN
        self.PRED_LEN = PRED_LEN
        self.WINDOW_LENGTH = SEQ_LEN + PRED_LEN

        self.DATES = pd.to_datetime(self.DATES, format='%Y-%m-%d')
        self.LEN = len(self.OT) - self.WINDOW_LENGTH + 1

        self.begin = self.DATES[0]
        self.end = self.DATES[self.LEN - 1]

        self.DATES = np.vstack([np.array([i.year, i.month, i.day]) for i in self.DATES])

    def __len__(self):
        return len(self.OT) - self.WINDOW_LENGTH + 1

    def __getitem__(self, index):
        s_begin = index
        s_end = s_begin + self.SEQ_LEN
        r_begin = s_end - self.LABEL_LEN
        r_end = r_begin + self.LABEL_LEN + self.PRED_LEN

        # seq_x = torch.Tensor(self.OT[s_begin:s_end]).reshape(-1, 1)
        # seq_y = torch.Tensor(self.OT[r_begin:r_end]).reshape(-1, 1)
        # seq_x_mark = torch.Tensor(self.DATES[s_begin:s_end])
        # seq_y_mark = torch.Tensor(self.DATES[r_begin:r_end])

        # return seq_x, seq_y, seq_x_mark, seq_y_mark

        seq_x = torch.Tensor(self.OT[s_begin:s_end]).reshape(-1, 1)
        seq_y = torch.Tensor(self.OT[r_begin:r_end]).reshape(-1, 1)
        seq_weathers = torch.Tensor(self.WEATHERS[s_begin:s_end])
        data_types = torch.Tensor(self.DAY_TYPES[s_begin:s_end])

        return seq_x + 1, seq_y + 1, ORG_CODE2index[int(self.ID)], data_types


class IndustryPowerSubDataset(Dataset):
    def __init__(self, instance_type, ID, HY_NAME, DATES, OT, SEQ_LEN, LABEL_LEN, PRED_LEN):
        self.ID = ID
        self.HY_NAME = list(HY_NAME)[0]
        self.DATES = DATES
        self.OT = OT
        self.instance_type = instance_type

        self.SEQ_LEN = SEQ_LEN
        self.LABEL_LEN = LABEL_LEN
        self.PRED_LEN = PRED_LEN
        self.WINDOW_LENGTH = SEQ_LEN + PRED_LEN

        self.DATES = pd.to_datetime(self.DATES, format='%Y-%m-%d %H:%M:%S')
        self.LEN = len(self.OT) - self.WINDOW_LENGTH + 1

        self.begin = self.DATES[0]
        self.end = self.DATES[self.LEN - 1]

        self.DATES = np.vstack([np.array([i.year, i.month, i.day]) for i in self.DATES])

    def __len__(self):
        return len(self.OT) - self.WINDOW_LENGTH + 1

    def __getitem__(self, index):
        s_begin = index
        s_end = s_begin + self.SEQ_LEN
        r_begin = s_end - self.LABEL_LEN
        r_end = r_begin + self.LABEL_LEN + self.PRED_LEN

        seq_x = torch.Tensor(self.OT[s_begin:s_end]).reshape(-1, 1)
        seq_y = torch.Tensor(self.OT[r_begin:r_end]).reshape(-1, 1)
        seq_x_mark = torch.Tensor(self.DATES[s_begin:s_end])
        seq_y_mark = torch.Tensor(self.DATES[r_begin:r_end])

        # return self.instance_type, seq_x+1, seq_y+1, seq_x_mark, seq_y_mark
        return seq_x+1, seq_y+1, seq_x_mark, seq_y_mark


class IndustryLoadSubDataset(Dataset):
    def __init__(self, instance_type, ENTITY_ID, MC, DATES, OT, SEQ_LEN, LABEL_LEN, PRED_LEN):
        self.ENTITY_ID = ENTITY_ID
        self.MC = MC
        self.DATES = DATES
        self.OT = OT
        self.instance_type = instance_type

        self.SEQ_LEN = SEQ_LEN
        self.LABEL_LEN = LABEL_LEN
        self.PRED_LEN = PRED_LEN
        self.WINDOW_LENGTH = SEQ_LEN + PRED_LEN

        self.OT = np.array(self.OT).reshape(-1)

        tbegin = pd.to_datetime(self.DATES[0], format='%Y-%m-%d')
        tend = pd.to_datetime(self.DATES[-1], format='%Y-%m-%d')
        self.DATES = pd.date_range(start=tbegin, end=tend + pd.Timedelta(days=1), freq='15T')[:-1]

        self.LEN = len(self.OT) - self.WINDOW_LENGTH + 1
        self.begin = self.DATES[0]
        self.end = self.DATES[self.LEN - 1]

        self.DATES = np.vstack([np.array([i.year, i.month, i.day, i.hour, i.minute]) for i in self.DATES])

    def __len__(self):
        return len(self.OT) - self.WINDOW_LENGTH + 1

    def __getitem__(self, index):
        s_begin = index
        s_end = s_begin + self.SEQ_LEN
        r_begin = s_end - self.LABEL_LEN
        r_end = r_begin + self.LABEL_LEN + self.PRED_LEN

        seq_x = torch.Tensor(self.OT[s_begin:s_end]).reshape(-1, 1)
        seq_y = torch.Tensor(self.OT[r_begin:r_end]).reshape(-1, 1)
        seq_x_mark = torch.Tensor(self.DATES[s_begin:s_end])
        seq_y_mark = torch.Tensor(self.DATES[r_begin:r_end])

        # return self.instance_type, seq_x+1, seq_y+1, seq_x_mark, seq_y_mark
        return seq_x+1, seq_y+1, seq_x_mark, seq_y_mark


class AreaLoadSubDataset(Dataset):
    def __init__(self, ENTITY_ID, CT, DATES, OT, SEQ_LEN, LABEL_LEN, PRED_LEN):
        # print(DATES)
        self.ENTITY_ID = ENTITY_ID
        self.CT = CT
        # print(self.ENTITY_ID)
        # print(self.CT)
        self.DATES = list(DATES)[0]
        # print(self.DATES)
        self.OT = list(OT)[0]
        # print(self.OT)

        self.SEQ_LEN = SEQ_LEN
        self.LABEL_LEN = LABEL_LEN
        self.PRED_LEN = PRED_LEN
        self.WINDOW_LENGTH = SEQ_LEN + PRED_LEN

        # print(self.OT)
        self.OT = np.array(self.OT).reshape(-1)

        tbegin = pd.to_datetime(self.DATES[0], format='%Y-%m-%d')
        tend = pd.to_datetime(self.DATES[-1], format='%Y-%m-%d')
        self.DATES = pd.date_range(start=tbegin, end=tend + pd.Timedelta(days=1), freq='15T')[:-1]

        self.LEN = len(self.OT) - self.WINDOW_LENGTH + 1
        self.begin = self.DATES[0]
        self.end = self.DATES[self.LEN - 1]

        self.DATES = np.vstack([np.array([i.year, i.month, i.day, i.hour, i.minute]) for i in self.DATES])

    def __len__(self):
        return len(self.OT) - self.WINDOW_LENGTH + 1

    def __getitem__(self, index):
        s_begin = index
        s_end = s_begin + self.SEQ_LEN
        r_begin = s_end - self.LABEL_LEN
        r_end = r_begin + self.LABEL_LEN + self.PRED_LEN

        seq_x = torch.Tensor(self.OT[s_begin:s_end]).reshape(-1, 1)
        seq_y = torch.Tensor(self.OT[r_begin:r_end]).reshape(-1, 1)
        seq_x_mark = torch.Tensor(self.DATES[s_begin:s_end])
        seq_y_mark = torch.Tensor(self.DATES[r_begin:r_end])

        return seq_x+1, seq_y+1, seq_x_mark, seq_y_mark



class AugPowerDataset(Dataset):
    def __init__(self, x, y):
        self.x = x
        self.y = y
    def __len__(self):
        return self.x.shape[0]
    def __getitem__(self, index):
        return self.x[index]+1, self.y[index]+1, 0, 0


class ProxyPowerDataset(Dataset):
    """
    代理购电数据集类
    用于处理代理购电.xlsx文件
    """
    def __init__(self, data_path, seq_len=288, label_len=48, pred_len=96,
                 target_col='代理购电', features='S', scale=True,
                 auto_feature_selection=True, correlation_file_path=None,
                 correlation_results_dir='feature_analysis_results'):
        """
        Args:
            data_path: Excel文件路径
            seq_len: 输入序列长度
            label_len: 标签长度
            pred_len: 预测长度
            target_col: 目标列名
            features: 特征类型 'S':单变量, 'M':多变量, 'MS':多变量预测单变量
            scale: 是否标准化
            auto_feature_selection: 是否启用MS模式的自动特征选择
            correlation_file_path: 指定的相关性分析文件路径
            correlation_results_dir: 相关性分析结果目录
        """
        self.seq_len = seq_len
        self.label_len = label_len
        self.pred_len = pred_len
        self.target_col = target_col
        self.features = features
        self.scale = scale
        self.auto_feature_selection = auto_feature_selection
        self.correlation_file_path = correlation_file_path
        self.correlation_results_dir = correlation_results_dir
        self.feature_selection_result = None

        # 读取Excel文件
        self.df_raw = pd.read_excel(data_path)

        # 处理日期列
        if '日期' in self.df_raw.columns:
            self.df_raw['日期'] = pd.to_datetime(self.df_raw['日期'].astype(str), format='%Y-%m-%d')
            self.df_raw = self.df_raw.sort_values('日期').reset_index(drop=True)

        # 清理和预处理天气数据
        self._clean_weather_data()

        # 选择特征列
        if features == 'S':  # 单变量预测
            self.data_columns = [target_col]
        elif features == 'M':  # 多变量预测多变量
            self.data_columns = self._get_numerical_columns()
        elif features == 'MS':  # 多变量预测单变量
            if auto_feature_selection:
                # 使用自动特征选择
                self._apply_automatic_feature_selection()
            else:
                # 使用所有数值特征（原有行为）
                self.data_columns = self._get_numerical_columns()

        # 提取数据
        self.data = self.df_raw[self.data_columns].values.astype(np.float32)

        # 数据标准化
        if self.scale:
            self.scaler = StandardScaler()
            self.data = self.scaler.fit_transform(self.data)

        # 生成时间特征（简化版）
        if '日期' in self.df_raw.columns:
            df_stamp = self.df_raw[['日期']].copy()
            df_stamp['month'] = df_stamp['日期'].dt.month
            df_stamp['day'] = df_stamp['日期'].dt.day
            df_stamp['weekday'] = df_stamp['日期'].dt.weekday
            df_stamp['hour'] = 0  # 日级数据，小时设为0
            self.data_stamp = df_stamp[['month', 'day', 'weekday', 'hour']].values
        else:
            # 如果没有日期列，生成简单的时间特征
            self.data_stamp = np.zeros((len(self.data), 4))

        print(f"数据形状: {self.data.shape}")
        print(f"时间特征形状: {self.data_stamp.shape}")
        print(f"特征列: {self.data_columns}")

        # 如果使用了自动特征选择，显示选择结果
        if self.feature_selection_result:
            print(f"特征选择方法: {self.feature_selection_result['selection_method']}")
            print(f"最终模式: {self.feature_selection_result['final_mode']}")

    def _apply_automatic_feature_selection(self):
        """应用自动特征选择"""
        try:
            # 导入特征选择器
            from feature_selector import auto_select_features_for_ms_mode

            # 获取所有可用的数值列（排除字符串列）
            all_data_columns = self._get_numerical_columns()

            # 执行自动特征选择
            self.feature_selection_result = auto_select_features_for_ms_mode(
                target_electricity_type=self.target_col,
                all_data_columns=all_data_columns,
                correlation_results_dir=self.correlation_results_dir,
                correlation_file_path=self.correlation_file_path
            )

            # 使用选择的特征
            self.data_columns = self.feature_selection_result['selected_features']

            # 如果建议回退到单变量模式，更新features属性
            if self.feature_selection_result['final_mode'] == 'S':
                print("Warning: 根据相关性分析结果，建议使用单变量(S)模式")
                print("如需强制使用MS模式，请设置 auto_feature_selection=False")
                self.features = 'S'
                self.data_columns = [self.target_col]
            else:
                # 应用相关性权重（如果可用）
                self._apply_correlation_weights()

        except ImportError:
            print("Warning: 无法导入feature_selector模块，使用所有特征")
            self.data_columns = [col for col in self.df_raw.columns if col != '日期']
        except Exception as e:
            print(f"Warning: 自动特征选择失败: {str(e)}")
            print("回退到使用所有数值特征")
            self.data_columns = self._get_numerical_columns()

    def _clean_weather_data(self):
        """清理和预处理天气数据"""
        print("清理天气数据...")

        # 处理温度字符串列（如 '20℃' -> 20.0）
        temp_columns = ['高温', '低温']
        for col in temp_columns:
            if col in self.df_raw.columns:
                # 移除℃符号并转换为数值
                self.df_raw[col] = self.df_raw[col].astype(str).str.replace('℃', '').str.replace('°C', '')
                # 尝试转换为数值，无法转换的设为NaN
                self.df_raw[col] = pd.to_numeric(self.df_raw[col], errors='coerce')
                print(f"  转换温度列 {col}: {self.df_raw[col].dtype}")

        # 处理天气状况列 - 转换为数值编码
        if '天气状况' in self.df_raw.columns:
            weather_mapping = {
                '晴': 1, '多云': 2, '阴': 3, '小雨': 4, '中雨': 5, '大雨': 6, '暴雨': 7,
                '晴/多云': 1.5, '多云/晴': 1.5, '多云/阴': 2.5, '阴/多云': 2.5,
                '雷阵雨': 4.5, '阵雨': 4, '未知': 0
            }

            # 创建数值编码列
            self.df_raw['天气状况_编码'] = self.df_raw['天气状况'].map(weather_mapping)
            # 对于未映射的值，设为0
            self.df_raw['天气状况_编码'] = self.df_raw['天气状况_编码'].fillna(0)
            print(f"  转换天气状况列: {self.df_raw['天气状况_编码'].dtype}")

        # 处理风向风力列 - 提取风力等级
        if '风向风力' in self.df_raw.columns:
            # 提取风力等级数字
            wind_force = self.df_raw['风向风力'].astype(str).str.extract(r'(\d+)级')[0]
            self.df_raw['风力等级'] = pd.to_numeric(wind_force, errors='coerce').fillna(0)
            print(f"  转换风向风力列: {self.df_raw['风力等级'].dtype}")

        # 确保所有数值列都是float类型
        numeric_columns = self.df_raw.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if col != '日期':
                self.df_raw[col] = self.df_raw[col].astype(float)

        print(f"数据清理完成，数据形状: {self.df_raw.shape}")

    def _get_numerical_columns(self):
        """获取所有数值列（排除日期列）"""
        # 排除日期列和原始字符串列
        exclude_cols = ['日期', '天气状况', '风向风力']
        numerical_cols = []

        for col in self.df_raw.columns:
            if col not in exclude_cols:
                # 检查是否为数值类型
                if pd.api.types.is_numeric_dtype(self.df_raw[col]):
                    numerical_cols.append(col)

        return numerical_cols

    def _apply_correlation_weights(self):
        """应用基于相关性的特征权重"""
        if not hasattr(self, 'feature_selection_result') or not self.feature_selection_result:
            return

        feature_weights = self.feature_selection_result.get('feature_weights', {})
        if not feature_weights:
            print("No correlation weights available, using equal weighting")
            return

        print(f"\n应用相关性权重到天气特征...")

        # 在数据标准化之前应用权重
        # 只对天气特征应用权重，目标变量保持不变
        for feature, weight in feature_weights.items():
            if feature in self.data_columns and feature != self.target_col:
                # 找到特征在数据中的索引
                feature_idx = self.data_columns.index(feature)

                # 应用权重到原始数据
                original_mean = self.df_raw[feature].mean()
                original_std = self.df_raw[feature].std()

                # 权重应用策略：将特征值乘以权重
                # 这会增强高相关性特征的影响，减弱低相关性特征的影响
                self.df_raw[feature] = self.df_raw[feature] * weight

                weighted_mean = self.df_raw[feature].mean()
                weighted_std = self.df_raw[feature].std()

                print(f"  {feature}: weight={weight:.3f}")
                print(f"    原始 mean={original_mean:.3f}, std={original_std:.3f}")
                print(f"    加权 mean={weighted_mean:.3f}, std={weighted_std:.3f}")

        print(f"相关性权重应用完成")

    def __len__(self):
        return len(self.data) - self.seq_len - self.pred_len + 1

    def __getitem__(self, index):
        s_begin = index
        s_end = s_begin + self.seq_len
        r_begin = s_end - self.label_len
        r_end = r_begin + self.label_len + self.pred_len

        seq_x = self.data[s_begin:s_end]
        seq_y = self.data[r_begin:r_end]
        seq_x_mark = self.data_stamp[s_begin:s_end]
        seq_y_mark = self.data_stamp[r_begin:r_end]

        return seq_x, seq_y, seq_x_mark, seq_y_mark

    def inverse_transform(self, data):
        """反标准化"""
        if self.scale:
            return self.scaler.inverse_transform(data)
        return data


