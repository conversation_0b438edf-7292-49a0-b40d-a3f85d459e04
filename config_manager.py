"""
配置管理模块
提供统一的配置管理和自动化适配功能
"""

import os
import pandas as pd
from typing import Dict, List, Tuple, Optional


class TargetConfig:
    """目标变量配置类"""
    def __init__(self, chinese_name: str, english_id: str, description: str):
        self.chinese_name = chinese_name  # 中文名称
        self.english_id = english_id      # 英文标识符
        self.description = description    # 描述信息


class ConfigManager:
    """配置管理器"""
    
    # 目标变量配置映射
    TARGET_CONFIGS = {
        '代理购电': TargetConfig('代理购电', 'proxy_power', '代理购电量预测'),
        '居民': TargetConfig('居民', 'resident', '居民用电量预测'),
        '农业': TargetConfig('农业', 'agriculture', '农业用电量预测'),
    }
    
    # 特征模式配置
    FEATURE_MODES = {
        'S': {'name': '单变量', 'description': '单变量输入单变量输出'},
        'M': {'name': '多变量', 'description': '多变量输入多变量输出'},
        'MS': {'name': '多变量单输出', 'description': '多变量输入单变量输出'}
    }
    
    # 数据文件配置
    DATA_FILE_PATH = './data/龙泉代理购电.xlsx'
    INTEGRATED_WEATHER_DATA_PATH = './data/整合天气数据_代理购电_20250716_094327.xlsx'

    @classmethod
    def get_data_file_path(cls, features: str = 'S', auto_feature_selection: bool = False) -> str:
        """根据特征模式和自动特征选择设置确定数据文件路径"""
        if features == 'MS' and auto_feature_selection:
            # MS模式且启用自动特征选择时，使用集成天气数据
            if os.path.exists(cls.INTEGRATED_WEATHER_DATA_PATH):
                return cls.INTEGRATED_WEATHER_DATA_PATH
            else:
                print(f"Warning: 集成天气数据文件不存在: {cls.INTEGRATED_WEATHER_DATA_PATH}")
                print("回退到基础电力数据文件")
                return cls.DATA_FILE_PATH
        else:
            # 其他情况使用基础电力数据
            return cls.DATA_FILE_PATH
    
    @classmethod
    def get_target_config(cls, target: str) -> TargetConfig:
        """获取目标变量配置"""
        if target not in cls.TARGET_CONFIGS:
            available_targets = list(cls.TARGET_CONFIGS.keys())
            raise ValueError(f"不支持的目标变量: {target}. 可用选项: {available_targets}")
        return cls.TARGET_CONFIGS[target]
    
    @classmethod
    def validate_target_in_data(cls, target: str, data_path: str = None) -> bool:
        """验证目标变量是否在数据文件中存在"""
        if data_path is None:
            data_path = cls.DATA_FILE_PATH
            
        if not os.path.exists(data_path):
            raise FileNotFoundError(f"数据文件不存在: {data_path}")
            
        try:
            df = pd.read_excel(data_path)
            available_columns = df.columns.tolist()
            
            if target not in available_columns:
                raise ValueError(f"目标列 '{target}' 不存在于数据文件中. 可用列: {available_columns}")
            
            return True
        except Exception as e:
            raise RuntimeError(f"读取数据文件失败: {str(e)}")
    
    @classmethod
    def get_available_columns(cls, data_path: str = None) -> List[str]:
        """获取数据文件中的所有列名"""
        if data_path is None:
            data_path = cls.DATA_FILE_PATH
            
        if not os.path.exists(data_path):
            return []
            
        try:
            df = pd.read_excel(data_path)
            return df.columns.tolist()
        except:
            return []
    
    @classmethod
    def auto_configure_dimensions(cls, features: str, target: str, data_path: str = None,
                                selected_features: List[str] = None) -> Dict[str, int]:
        """根据特征模式和目标变量自动配置模型维度"""
        if data_path is None:
            data_path = cls.DATA_FILE_PATH

        # 验证目标变量
        cls.validate_target_in_data(target, data_path)

        # 获取数据列信息
        df = pd.read_excel(data_path)
        all_columns = df.columns.tolist()

        # 排除日期列
        data_columns = [col for col in all_columns if col != '日期']

        # 如果提供了选择的特征，使用它们
        if selected_features is not None:
            data_columns = selected_features
        
        if features == 'S':
            # 单变量：只使用目标列
            enc_in = 1
            dec_in = 1
            c_out = 1
        elif features == 'M':
            # 多变量：使用所有数据列
            enc_in = len(data_columns)
            dec_in = len(data_columns)
            c_out = len(data_columns)
        elif features == 'MS':
            # 多变量输入单变量输出：输入所有列，输出目标列
            enc_in = len(data_columns)
            dec_in = len(data_columns)
            c_out = 1
        else:
            raise ValueError(f"不支持的特征模式: {features}. 可用选项: S, M, MS")
        
        return {
            'enc_in': enc_in,
            'dec_in': dec_in,
            'c_out': c_out,
            'data_columns': data_columns,
            'target_column': target
        }
    
    @classmethod
    def generate_model_id(cls, target: str, pred_len: int, features: str = 'S') -> str:
        """生成模型ID"""
        target_config = cls.get_target_config(target)
        feature_suffix = f"_{features.lower()}" if features != 'S' else ""
        
        if pred_len == 1:
            time_suffix = "1d"
        elif pred_len == 7:
            time_suffix = "7d"
        elif pred_len == 30:
            time_suffix = "30d"
        elif pred_len == 50:
            time_suffix = "50d"
        elif pred_len == 20:
            time_suffix = "20d"
        else:
            time_suffix = f"{pred_len}d"
        
        return f"{target_config.english_id}_{time_suffix}{feature_suffix}"
    
    @classmethod
    def generate_file_names(cls, target: str, pred_len: int, features: str = 'S') -> Dict[str, str]:
        """生成输出文件名"""
        target_config = cls.get_target_config(target)
        model_id = cls.generate_model_id(target, pred_len, features)
        
        return {
            'csv': f"prediction_{model_id}.csv",
            'png': f"prediction_{model_id}.png",
            'model_dir': f"long_term_sg_forecast_{model_id}_PatchTST_{target_config.english_id}"
        }
    
    @classmethod
    def get_display_names(cls, target: str, features: str = 'S') -> Dict[str, str]:
        """获取显示名称"""
        target_config = cls.get_target_config(target)
        feature_mode = cls.FEATURE_MODES.get(features, {'name': '未知'})
        
        return {
            'target_name': target_config.chinese_name,
            'target_description': target_config.description,
            'feature_mode': feature_mode['name'],
            'feature_description': feature_mode['description'],
            'chart_title': f"{target_config.chinese_name}预测结果",
            'y_label': f"{target_config.chinese_name}量",
            'prediction_label': f"预测{target_config.chinese_name}"
        }
    
    @classmethod
    def get_weight_path_pattern(cls, target: str, pred_len: int, features: str = 'S') -> str:
        """获取模型权重文件路径模式"""
        file_names = cls.generate_file_names(target, pred_len, features)
        base_pattern = f"./checkpoints/{file_names['model_dir']}"
        
        # 由于实际的checkpoint目录名可能包含更多参数，这里提供一个模式
        return f"{base_pattern}*/checkpoint.pth"
    
    @classmethod
    def find_weight_file(cls, target: str, pred_len: int, features: str = 'S') -> Optional[str]:
        """查找实际的权重文件"""
        if not os.path.exists("./checkpoints/"):
            return None

        # 获取目标配置
        target_config = cls.get_target_config(target)

        # 构建搜索模式
        # 实际的目录名格式：long_term_sg_forecast_{model_id}_PatchTST_...
        model_id = cls.generate_model_id(target, pred_len, features)

        # 搜索匹配的目录
        for item in os.listdir("./checkpoints/"):
            # 检查目录名是否包含我们要找的模式
            if f"long_term_sg_forecast_{target_config.english_id}_{pred_len}d" in item:
                # 检查特征模式是否匹配
                if features == 'MS' and '_ms_' in item.lower():
                    checkpoint_path = os.path.join("./checkpoints/", item, "checkpoint.pth")
                    if os.path.exists(checkpoint_path):
                        return checkpoint_path
                elif features == 'S' and '_ms_' not in item.lower():
                    checkpoint_path = os.path.join("./checkpoints/", item, "checkpoint.pth")
                    if os.path.exists(checkpoint_path):
                        return checkpoint_path

        return None
    
    @classmethod
    def print_configuration_summary(cls, target: str, features: str, pred_len: int, data_path: str = None):
        """打印配置摘要"""
        print("=" * 60)
        print("配置摘要")
        print("=" * 60)
        
        # 目标变量信息
        target_config = cls.get_target_config(target)
        print(f"目标变量: {target_config.chinese_name} ({target_config.english_id})")
        print(f"描述: {target_config.description}")
        
        # 特征模式信息
        feature_info = cls.FEATURE_MODES.get(features, {'name': '未知', 'description': '未知模式'})
        print(f"特征模式: {features} - {feature_info['name']}")
        print(f"模式描述: {feature_info['description']}")
        
        # 预测配置
        print(f"预测长度: {pred_len} 天")
        
        # 维度配置
        try:
            dim_config = cls.auto_configure_dimensions(features, target, data_path)
            print(f"输入维度: {dim_config['enc_in']}")
            print(f"输出维度: {dim_config['c_out']}")
            print(f"数据列: {dim_config['data_columns']}")
        except Exception as e:
            print(f"维度配置错误: {str(e)}")
        
        # 文件名配置
        file_names = cls.generate_file_names(target, pred_len, features)
        print(f"模型ID: {cls.generate_model_id(target, pred_len, features)}")
        print(f"输出文件: {file_names['csv']}, {file_names['png']}")
        
        print("=" * 60)
