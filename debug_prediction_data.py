#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug script to investigate prediction data loading issues
"""

import pandas as pd
from config_manager import ConfigManager
from data_provider.sg_dataset import ProxyPowerDataset
import os

def debug_data_file_paths():
    """Debug data file path selection"""
    print("=" * 80)
    print("DEBUGGING DATA FILE PATH SELECTION")
    print("=" * 80)
    
    # Test different feature modes
    for features in ['S', 'M', 'MS']:
        for auto_feature_selection in [True, False]:
            data_path = ConfigManager.get_data_file_path(features, auto_feature_selection)
            exists = os.path.exists(data_path)
            print(f"Features: {features}, Auto: {auto_feature_selection}")
            print(f"  Path: {data_path}")
            print(f"  Exists: {exists}")
            
            if exists:
                try:
                    df = pd.read_excel(data_path)
                    print(f"  Columns: {df.columns.tolist()}")
                    print(f"  Shape: {df.shape}")
                except Exception as e:
                    print(f"  Error loading: {str(e)}")
            print()

def debug_dataset_loading(target='代理购电', features='MS'):
    """Debug dataset loading for prediction"""
    print("=" * 80)
    print(f"DEBUGGING DATASET LOADING FOR PREDICTION")
    print(f"Target: {target}, Features: {features}")
    print("=" * 80)
    
    # Get the data path that should be used for prediction
    data_path = ConfigManager.get_data_file_path(features, auto_feature_selection=True)
    print(f"Data path for prediction: {data_path}")
    print(f"File exists: {os.path.exists(data_path)}")
    
    if not os.path.exists(data_path):
        print("ERROR: Data file does not exist!")
        return
    
    # Load raw data to check columns
    print(f"\n1. Raw data inspection:")
    df_raw = pd.read_excel(data_path)
    print(f"  Shape: {df_raw.shape}")
    print(f"  Columns: {df_raw.columns.tolist()}")
    print(f"  Target '{target}' in columns: {target in df_raw.columns}")
    
    # Test dataset creation
    print(f"\n2. Dataset creation test:")
    try:
        dataset = ProxyPowerDataset(
            data_path=data_path,
            seq_len=96,
            label_len=24,
            pred_len=7,
            target_col=target,
            features=features,
            scale=True,
            auto_feature_selection=True,
            correlation_file_path=None,
            correlation_results_dir='feature_analysis_results'
        )
        
        print(f"  ✓ Dataset created successfully")
        print(f"  Data shape: {dataset.data.shape}")
        print(f"  Features mode: {dataset.features}")
        print(f"  Selected columns: {dataset.data_columns}")
        print(f"  Target in selected columns: {target in dataset.data_columns}")
        
        # Check feature selection result
        if hasattr(dataset, 'feature_selection_result') and dataset.feature_selection_result:
            result = dataset.feature_selection_result
            print(f"  Feature selection method: {result['selection_method']}")
            print(f"  Final mode: {result['final_mode']}")
            print(f"  Selected features: {result['selected_features']}")
        
        # Test data access
        print(f"\n3. Data access test:")
        try:
            sample = dataset[0]
            print(f"  ✓ Sample data loaded")
            print(f"  Input shape: {sample[0].shape}")
            print(f"  Output shape: {sample[1].shape}")
        except Exception as e:
            print(f"  ✗ Data access failed: {str(e)}")
            
    except Exception as e:
        print(f"  ✗ Dataset creation failed: {str(e)}")
        import traceback
        traceback.print_exc()

def debug_inference_pipeline(target='代理购电', features='MS'):
    """Debug the complete inference pipeline"""
    print("=" * 80)
    print(f"DEBUGGING COMPLETE INFERENCE PIPELINE")
    print(f"Target: {target}, Features: {features}")
    print("=" * 80)
    
    try:
        # Import inference components
        from inference_proxy_power import PowerForecastInference, get_model_configs
        
        # Get model configuration
        print("1. Getting model configuration...")
        configs = get_model_configs(target, features, pred_len=7)
        print(f"  ✓ Model configs created")
        print(f"  Target: {configs.target}")
        print(f"  Features: {configs.features}")
        print(f"  Pred len: {configs.pred_len}")
        
        # Get data path
        print(f"\n2. Getting data path...")
        data_path = ConfigManager.get_data_file_path(features, auto_feature_selection=True)
        print(f"  Data path: {data_path}")
        print(f"  File exists: {os.path.exists(data_path)}")
        
        if not os.path.exists(data_path):
            print("  ✗ Data file does not exist!")
            return
        
        # Create inference object
        print(f"\n3. Creating inference object...")
        inference = PowerForecastInference(configs)
        print(f"  ✓ Inference object created")
        
        # Test data preparation
        print(f"\n4. Testing data preparation...")
        try:
            seq_x, seq_x_mark, dataset = inference.prepare_data(data_path, seq_len=96)
            print(f"  ✓ Data preparation successful")
            print(f"  Input shape: {seq_x.shape}")
            print(f"  Dataset columns: {dataset.data_columns}")
            print(f"  Target in columns: {target in dataset.data_columns}")
            
            if hasattr(dataset, 'feature_selection_result') and dataset.feature_selection_result:
                result = dataset.feature_selection_result
                print(f"  Feature selection: {result['selection_method']}")
                print(f"  Selected features: {result['selected_features']}")
            
        except Exception as e:
            print(f"  ✗ Data preparation failed: {str(e)}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"✗ Inference pipeline debug failed: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """Main debug function"""
    print("PREDICTION DATA LOADING DEBUG SCRIPT")
    print("=" * 80)
    
    # Debug 1: Data file paths
    debug_data_file_paths()
    
    # Debug 2: Dataset loading
    debug_dataset_loading('代理购电', 'MS')
    
    # Debug 3: Complete inference pipeline
    debug_inference_pipeline('代理购电', 'MS')
    
    print("\n" + "=" * 80)
    print("DEBUG COMPLETE")
    print("=" * 80)

if __name__ == "__main__":
    main()
