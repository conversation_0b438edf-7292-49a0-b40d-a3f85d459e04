import torch
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from dataclasses import dataclass
from models.PatchTST import Model
from data_provider.sg_dataset import ProxyPowerDataset
from config_manager import ConfigManager
import warnings

warnings.filterwarnings("ignore")

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号

@dataclass
class PowerForecastConfigs:
    """电力预测配置类 - 支持自动化配置"""
    seq_len: int = 96
    pred_len: int = 1
    e_layers: int = 2
    d_layers: int = 1
    factor: int = 3
    d_model: int = 512
    dropout: float = 0.1
    d_ff: int = 2048
    activation: str = 'gelu'
    n_heads: int = 8
    output_attention: bool = False
    weight_path: str = None
    enc_in: int = 1
    dec_in: int = 1
    c_out: int = 1
    label_len: int = 24
    embed: str = 'timeF'
    distil: bool = True

    # 新增配置字段
    target: str = '农业'
    features: str = 'S'

    @classmethod
    def create_from_target(cls, target: str, features: str = 'S', pred_len: int = 1):
        """根据目标变量自动创建配置"""
        # 获取维度配置
        dim_config = ConfigManager.auto_configure_dimensions(features, target)

        # 查找权重文件
        weight_path = ConfigManager.find_weight_file(target, pred_len, features)

        return cls(
            pred_len=pred_len,
            enc_in=dim_config['enc_in'],
            dec_in=dim_config['dec_in'],
            c_out=dim_config['c_out'],
            weight_path=weight_path,
            target=target,
            features=features
        )

class PowerForecastInference:
    """电力预测推理类 - 支持自动化配置"""

    def __init__(self, configs: PowerForecastConfigs):
        self.configs = configs
        self.target_config = ConfigManager.get_target_config(configs.target)
        self.display_names = ConfigManager.get_display_names(configs.target, configs.features)

        # 创建模型
        self.model = Model(configs=configs)

        # 加载模型权重
        if configs.weight_path and os.path.exists(configs.weight_path):
            checkpoint = torch.load(configs.weight_path, map_location=torch.device('cpu'))
            self.model.load_state_dict(checkpoint)
            print(f"成功加载模型权重: {configs.weight_path}")
        else:
            available_files = ConfigManager.find_weight_file(configs.target, configs.pred_len, configs.features)
            if available_files:
                print(f"警告：指定的权重文件不存在，尝试使用: {available_files}")
                checkpoint = torch.load(available_files, map_location=torch.device('cpu'))
                self.model.load_state_dict(checkpoint)
            else:
                raise FileNotFoundError(f"找不到模型权重文件。请先训练模型。")

        self.model.eval()

        # 设备配置
        self.device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
        self.model = self.model.to(self.device)
        print(f"使用设备: {self.device}")
        print(f"目标变量: {self.display_names['target_name']}")
        print(f"特征模式: {self.display_names['feature_mode']}")
    
    def prepare_data(self, data_path, seq_len=96):
        """准备推理数据"""
        # 加载数据集，确保使用与训练时相同的特征选择
        dataset = ProxyPowerDataset(
            data_path=data_path,
            seq_len=seq_len,
            label_len=self.configs.label_len,
            pred_len=self.configs.pred_len,
            target_col=self.configs.target,
            features=self.configs.features,
            scale=True,
            auto_feature_selection=True,  # 启用自动特征选择
            correlation_file_path=None,   # 使用最新的相关性文件
            correlation_results_dir='feature_analysis_results'
        )

        # 获取最后一个序列作为输入
        last_idx = len(dataset) - 1
        seq_x, seq_y, seq_x_mark, seq_y_mark = dataset[last_idx]

        # 转换为tensor并添加batch维度
        seq_x = torch.FloatTensor(seq_x).unsqueeze(0).to(self.device)
        seq_x_mark = torch.FloatTensor(seq_x_mark).unsqueeze(0).to(self.device)

        # 如果使用了自动特征选择，显示选择结果
        if hasattr(dataset, 'feature_selection_result') and dataset.feature_selection_result:
            print(f"推理时特征选择结果:")
            print(f"  选择方法: {dataset.feature_selection_result['selection_method']}")
            print(f"  最终模式: {dataset.feature_selection_result['final_mode']}")
            print(f"  选择的特征数: {len(dataset.feature_selection_result['selected_features'])}")

        return seq_x, seq_x_mark, dataset
    
    def predict(self, data_path):
        """执行预测"""
        # 准备数据
        seq_x, seq_x_mark, dataset = self.prepare_data(data_path, self.configs.seq_len)

        print(f"输入数据形状: {seq_x.shape}")
        print(f"预测长度: {self.configs.pred_len}")

        # 执行预测
        with torch.no_grad():
            prediction = self.model(seq_x)  # PatchTST只需要输入序列

        print(f"模型原始输出形状: {prediction.shape}")

        # 转换为numpy数组
        prediction = prediction.detach().cpu().numpy().squeeze()
        print(f"squeeze后预测形状: {prediction.shape}")

        # 反标准化
        if hasattr(dataset, 'scaler') and dataset.scale:
            if self.configs.features == 'MS':
                # MS模式：多变量输入单变量输出
                # 需要找到目标列在数据列中的索引
                target_idx = dataset.data_columns.index(self.configs.target)
                print(f"目标列索引: {target_idx}")
                print(f"数据列: {dataset.data_columns}")

                # 处理MS模式的预测结果
                if prediction.ndim == 1:
                    # 如果预测结果是1维的，说明模型正确输出了单个目标变量
                    # 创建一个与scaler训练时相同维度的数组，但只填充目标列
                    dummy_data = np.zeros((len(prediction), len(dataset.data_columns)))
                    dummy_data[:, target_idx] = prediction
                    prediction_original = dataset.scaler.inverse_transform(dummy_data)[:, target_idx]
                elif prediction.ndim == 2:
                    # 如果预测结果是2维的，说明模型输出了所有特征维度
                    # 只取目标列的预测结果
                    if prediction.shape[1] == len(dataset.data_columns):
                        # 模型输出了所有特征，只取目标列
                        target_prediction = prediction[:, target_idx]
                        # 创建dummy数组进行反标准化
                        dummy_data = np.zeros((len(target_prediction), len(dataset.data_columns)))
                        dummy_data[:, target_idx] = target_prediction
                        prediction_original = dataset.scaler.inverse_transform(dummy_data)[:, target_idx]
                    elif prediction.shape[1] == 1:
                        # 模型输出了单个特征，直接使用
                        target_prediction = prediction[:, 0]
                        dummy_data = np.zeros((len(target_prediction), len(dataset.data_columns)))
                        dummy_data[:, target_idx] = target_prediction
                        prediction_original = dataset.scaler.inverse_transform(dummy_data)[:, target_idx]
                    else:
                        raise ValueError(f"MS模式下预测结果维度不匹配: {prediction.shape}")
                else:
                    # 单个值的情况
                    if prediction.ndim == 0:
                        dummy_data = np.zeros((1, len(dataset.data_columns)))
                        dummy_data[0, target_idx] = prediction
                        prediction_original = dataset.scaler.inverse_transform(dummy_data)[0, target_idx]
                    else:
                        raise ValueError(f"MS模式下预测结果维度异常: {prediction.shape}")
            else:
                # S或M模式：直接反标准化
                if prediction.ndim == 0:  # 单个值
                    pred_reshaped = prediction.reshape(1, 1)
                else:
                    pred_reshaped = prediction.reshape(-1, 1)

                prediction_original = dataset.scaler.inverse_transform(pred_reshaped).flatten()
        else:
            prediction_original = prediction

        return prediction_original, dataset
    
    def visualize_prediction(self, data_path, save_path=None):
        """可视化预测结果"""
        # 执行预测
        prediction, dataset = self.predict(data_path)
        
        # 获取历史数据
        df_raw = pd.read_excel(data_path)
        historical_data = df_raw[self.configs.target].values
        
        # 创建日期序列
        if '日期' in df_raw.columns:
            dates = pd.to_datetime(df_raw['日期'].astype(str), format='%Y-%m-%d')
            last_date = dates.iloc[-1]
            # 生成未来日期
            future_dates = pd.date_range(start=last_date + pd.Timedelta(days=1), 
                                       periods=len(prediction), freq='D')
        else:
            dates = range(len(historical_data))
            future_dates = range(len(historical_data), len(historical_data) + len(prediction))
        
        # 绘图
        plt.figure(figsize=(15, 8))
        
        # 绘制历史数据（最后100个点）
        history_len = min(100, len(historical_data))
        if '日期' in df_raw.columns:
            plt.plot(dates[-history_len:], historical_data[-history_len:], 
                    label='历史数据', color='blue', linewidth=2)
            plt.plot(future_dates, prediction, 
                    label=f'预测数据({self.configs.pred_len}天)', 
                    color='red', linewidth=2, marker='o')
        else:
            plt.plot(range(len(historical_data)-history_len, len(historical_data)), 
                    historical_data[-history_len:], 
                    label='历史数据', color='blue', linewidth=2)
            plt.plot(range(len(historical_data), len(historical_data) + len(prediction)), 
                    prediction, 
                    label=f'预测数据({self.configs.pred_len}天)', 
                    color='red', linewidth=2, marker='o')
        
        plt.title(f'{self.display_names["chart_title"]} (预测{self.configs.pred_len}天)', fontsize=16)
        plt.xlabel('日期', fontsize=12)
        plt.ylabel(self.display_names['y_label'], fontsize=12)
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"预测图表已保存: {save_path}")

        plt.show()

        return prediction

def get_model_configs(target='农业', features='S', pred_len=1):
    """根据目标变量和预测长度获取模型配置"""
    return PowerForecastConfigs.create_from_target(target, features, pred_len)

def main(target='农业', features='S'):
    """主函数 - 支持自动化配置"""

    # 获取显示名称
    try:
        display_names = ConfigManager.get_display_names(target, features)
        ConfigManager.validate_target_in_data(target)
    except Exception as e:
        print(f"配置错误: {str(e)}")
        print("可用的目标变量:", list(ConfigManager.TARGET_CONFIGS.keys()))
        print("可用的特征模式: S, M, MS")
        return

    print(f"{display_names['target_description']}推理")
    print("="*60)

    # 根据特征模式确定数据文件路径
    data_path = ConfigManager.get_data_file_path(features, auto_feature_selection=True)

    # 检查数据文件
    if not os.path.exists(data_path):
        print(f"错误：数据文件 {data_path} 不存在！")
        return

    # 可用的预测模式
    prediction_modes = {
        1: "1天预测",
        7: "7天预测",
        15: "15天预测",
        20: "20天预测",
        30: "30天预测",
        50: "50天预测",
        56: "56天预测"
    }

    print("可用的预测模式:")
    for mode, desc in prediction_modes.items():
        print(f"  {mode}: {desc}")

    # 选择预测模式
    try:
        mode = int(input(f"\n请选择{display_names['target_name']}预测模式 (1/7/15/20/30/50/56): "))
        if mode not in prediction_modes:
            print("无效的预测模式！")
            return
    except ValueError:
        print("请输入有效的数字！")
        return

    try:
        # 获取配置
        configs = get_model_configs(target, features, mode)

        # 检查模型文件
        if not configs.weight_path:
            print(f"错误：找不到{display_names['target_name']}的模型权重文件")
            print("请先运行训练脚本训练模型！")
            return

        # 创建推理器
        inference = PowerForecastInference(configs)

        # 生成文件名
        file_names = ConfigManager.generate_file_names(target, mode, features)

        # 执行预测并可视化
        prediction = inference.visualize_prediction(data_path, file_names['png'])

        # 保存预测结果
        result_df = pd.DataFrame({
            '预测天数': range(1, len(prediction) + 1),
            f'预测{display_names["target_name"]}': prediction
        })

        result_df.to_csv(file_names['csv'], index=False, encoding='utf-8-sig')

        print(f"\n{display_names['target_name']}预测结果:")
        print(result_df)
        print(f"\n预测结果已保存: {file_names['csv']}")

        # 返回预测信息，供调用者进行后续处理
        return {
            'pred_len': mode,
            'csv_path': file_names['csv'],
            'target': target,
            'features': features
        }

    except Exception as e:
        print(f"预测过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == '__main__':
    # 支持命令行参数
    import sys
    if len(sys.argv) >= 2:
        target = sys.argv[1]
        features = sys.argv[2] if len(sys.argv) >= 3 else 'S'
        main(target, features)
    else:
        # 默认配置
        main()
